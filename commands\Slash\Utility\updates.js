const { SlashCommandBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

module.exports = {
    conf: {
        Slash: {
            enabled: true,
            userPermissions: [],
            botPermissions: [],
        },
    },
    help: {
        Slash: {
            data: new SlashCommandBuilder()
                .setName('updates')
                .setDescription('Show the latest updates and changes to the bot!'),
        },
    },
    slashRun: async (interaction) => {
        const updatesPath = path.join(__dirname, '../../../updates.json');
        let updates = [];
        try {
            updates = JSON.parse(fs.readFileSync(updatesPath, 'utf8'));
        } catch (err) {
            return interaction.reply({ content: 'No updates found or failed to load updates.', ephemeral: true });
        }

        const embed = {
            title: 'Star-Gate Bot Updates',
            description: 'Here are the latest updates and changes:',
            color: 0x3498DB,
            fields: updates.map(update => ({
                name: `${update.date} - ${update.title}`,
                value: update.description
            }))
        };

        await interaction.reply({ embeds: [embed], ephemeral: false });
    },
};
