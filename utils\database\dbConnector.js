const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

async function setupDatabase(client) {
    try {
        if (!client.config?.mongoDB?.enabled || !client.config?.mongoDB?.mongoURI) {
            client.logs.warn('No MongoDB configuration found or MongoDB is disabled');
            return false;
        }
        return setupMongoDB(client);
    } catch (error) {
        client.logs.error(`DB error: ${error.message}`);
        return false;
    }
}

async function setupMongoDB(client, retries = 3, delay = 5000) {
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            if (mongoose.connection.readyState === 1) {
                await mongoose.disconnect();
            }
            
            await mongoose.connect(client.config.mongoDB.mongoURI, {
                serverSelectionTimeoutMS: 5000,
                connectTimeoutMS: 10000,
                socketTimeoutMS: 45000
            });
            
            mongoose.connection.on('disconnected', async () => {
                client.logs.warn('MongoD<PERSON> disconnected, attempting to reconnect...');
                await setupMongoDB(client);
            });
            
            client.logs.database('Database connected (MongoDB)');
            await createSchemaFolder(client);
            return;
        } catch (error) {
            client.logs.error(`MongoDB connection error: ${error.message}`);
            if (attempt === retries) {
                client.logs.error(`Failed to connect to MongoDB after ${retries} attempts`);
                process.exit(1);
            }
            const retryDelay = delay * Math.pow(2, attempt - 1);
            client.logs.warn(`MongoDB connection attempt ${attempt} failed, retrying in ${retryDelay / 1000}s...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
}

async function createSchemaFolder(client) {
    const schemaPath = path.join(__dirname, '..', '..', 'schemas');
    try {
        await fs.promises.mkdir(schemaPath, { recursive: true });
    } catch (error) {
        client.logs.error(`Error creating schema folder: ${error.message}`);
    }
}

module.exports = { setupDatabase };
