// const User = require('../../schemas/addBadge.js');
const { ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder } = require('discord.js');
const Permissions = require('../../utils/checkPermissions.js');

module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'messageCreate'
};

module.exports.eventRun = async (message, client) => {
    try {
        const prefix = client.config.commands.prefix;

        if (!message || message.author.bot) return; 

        // ✅ Prefix command handling
        if (!message.content.startsWith(prefix)) return;

        const args = message.content.slice(prefix.length).trim().split(/\s+/);
        const commandName = args.length > 0 ? args.shift()?.toLowerCase() : null;
        if (!commandName) return;

        const command = client.prefix_commands.get(commandName) ||
            client.prefix_commands.find(cmd => cmd.conf?.Prefix?.aliases?.includes(commandName));
        if (!command) return;

        const { userPermissions, botPermissions, isDefaultCooldown, isPremiumCooldown } = command.conf?.Prefix || {};

        const userCooldownKey = `${message.author.id}-${commandName}`;
        const userPremiumStatus = await User.findOne({ userID: message.author.id });
        let cooldown = isDefaultCooldown;

        if (userPremiumStatus && userPremiumStatus.FLAGS.includes("PREMIUM") && isPremiumCooldown) {
            cooldown = isPremiumCooldown;
        }

        if (isDefaultCooldown) {
            // Initialize cooldowns Map if it doesn't exist
            if (!client.cooldowns) {
                client.cooldowns = new Map();
            }

            const lastUsed = client.cooldowns.get(userCooldownKey);
            if (lastUsed && lastUsed > Date.now()) {
                const timeLeft = (lastUsed - Date.now()) / 1000;
                const embed = client.embed()
                    .setColor(client.config.discord.defaultColor)
                    .setTitle("Woahhh! Slow down there Mr. Speedy Gonzales!")
                    .setDescription(`You can run this command again <t:${Math.floor(lastUsed / 1000)}:R>`)
                    .addFields(
                        { name: "Default Cooldown", value: `The Default Cooldown is **${isDefaultCooldown / 1000} Seconds**`, inline: true },
                        { name: "Premium Cooldown", value: `The Premium Cooldown is **${isPremiumCooldown / 1000} Seconds**`, inline: true }
                    );
                return message.reply({ embeds: [embed] });
            }

            // Set cooldown
            const expirationTime = Date.now() + cooldown;
            client.cooldowns.set(userCooldownKey, expirationTime);

            // Clean up expired cooldowns after the cooldown period
            setTimeout(() => {
                client.cooldowns.delete(userCooldownKey);
            }, cooldown);
        }

        if (userPermissions) {
            const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
            if (!ownerIds.includes(message.author.id)) {
                const missingUserPermissions = userPermissions.filter(perm => !message.member.permissions.has(Permissions[perm]));
                if (missingUserPermissions.length > 0) {
                    return client.errNormal({
                        error: `You are missing the following permissions: ${missingUserPermissions.join(', ')}`,
                        type: 'reply'
                    }, message);
                }
            }
        }

        if (botPermissions) {
            const botMember = message.guild.members.me;
            const missingBotPermissions = botPermissions.filter(perm => !botMember.permissions.has(Permissions[perm]));
            if (missingBotPermissions.length > 0) {
                return client.errNormal({
                    error: `I am missing the following permissions: ${missingBotPermissions.join(', ')}`,
                    type: 'reply'
                }, message);
            }
        }

        await command.prefixRun(message, client, args);
    } catch (error) {
        client.logs.error(error);
        client.errNormal({
            error: "An error occurred while executing the command",
            type: 'reply'
        }, message);
    }
};
