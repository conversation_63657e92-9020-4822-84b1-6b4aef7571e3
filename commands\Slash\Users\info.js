const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('info')
            .setDescription('Get basic information about the bot'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        const totalCommands = client.application_commands.size;
        const totalServers = client.guilds.cache.size;
        const totalUsers = client.users.cache.size;

        const embed = new EmbedBuilder()
            .setTitle('🤖 Bot Information')
            .setDescription('Learn more about Nexoria Restrictor and its features.')
            .addFields(
                { name: 'Total Commands', value: `${totalCommands}`, inline: true },
                { name: 'Total Servers', value: `${totalServers}`, inline: true },
                { name: 'Total Users', value: `${totalUsers}`, inline: true },
                { name: 'Purpose', value: 'Nexoria Restrictor is a moderation bot designed to manage restrictions, appeals, and server moderation efficiently.' },
                { name: 'Support', value: `[Support Server](${process.env.support || 'https://discord.gg/Ured4CPqeD'})`, inline: true },
                { name: 'Website', value: `[Visit Website](${process.env.website || 'https://nexoriadevelopment.com'})`, inline: true }
            )
            .setColor('#5865F2')
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Error executing info command:', error);

        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    ephemeral: true,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
