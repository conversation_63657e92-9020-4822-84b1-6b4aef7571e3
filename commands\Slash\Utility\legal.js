const {
    <PERSON>lash<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    ActionRowBuilder,
    StringSelectMenuBuilder,
    ComponentType,
    MessageFlags
} = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('legal')
            .setDescription('Displays legal information and links for TOS, Privacy Policy, GDPR, and our Website'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const tosURL = process.env.TOS || 'https://yourwebsite.com/terms';
        const privacyURL = process.env.PRIVACY || 'https://yourwebsite.com/privacy';
        const gdprURL = process.env.GDPR || 'https://yourwebsite.com/gdpr';
        const websiteURL = process.env.WEBSITE || 'https://yourwebsite.com';

        const embed = client.embed()
            .setTitle('Legal Information')
            .setDescription('Use the dropdown below to access our legal documents.')
            .setFooter({ text: process.env.COPYRIGHT || '© Your Company' })
            .setColor('#5865F2');

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('legal_select')
            .setPlaceholder('Select a document to view')
            .addOptions([
                {
                    label: 'Terms of Service',
                    description: 'View our Terms of Service',
                    value: 'tos',
                },
                {
                    label: 'Privacy Policy',
                    description: 'View our Privacy Policy',
                    value: 'privacy',
                },
                {
                    label: 'GDPR Information',
                    description: 'View GDPR compliance info',
                    value: 'gdpr',
                },
                {
                    label: 'Website',
                    description: 'Visit our website',
                    value: 'website',
                },
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const reply = await interaction.editReply({ embeds: [embed], components: [row] });

        const collector = reply.createMessageComponentCollector({
            componentType: ComponentType.StringSelect,
            time: 60_000, // 1 minute
        });

        collector.on('collect', async (selectInteraction) => {
            if (selectInteraction.user.id !== interaction.user.id) {
                return selectInteraction.reply({
                    content: 'Only the command user can interact with this menu.',
                    ephemeral: true,
                });
            }

            let selectedURL;
            switch (selectInteraction.values[0]) {
                case 'tos':
                    selectedURL = tosURL;
                    break;
                case 'privacy':
                    selectedURL = privacyURL;
                    break;
                case 'gdpr':
                    selectedURL = gdprURL;
                    break;
                case 'website':
                    selectedURL = websiteURL;
                    break;
            }

            await selectInteraction.reply({
                content: `Here is the link you requested: ${selectedURL}`,
                ephemeral: true,
            });
        });

        collector.on('end', () => {
            // Disable menu after timeout
            const disabledMenu = selectMenu.setDisabled(true);
            const disabledRow = new ActionRowBuilder().addComponents(disabledMenu);
            interaction.editReply({ components: [disabledRow] }).catch(() => {});
        });

    } catch (error) {
        console.error('Error executing legal command:', error);

        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    flags: MessageFlags.Ephemeral,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
