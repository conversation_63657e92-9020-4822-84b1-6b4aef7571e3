const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require('discord.js');
const RestrictionUserData = require('../../../schemas/restrictionUserData'); // Import RestrictionUserData schema
const Settings = require('../../../schemas/settings'); // Import the settings schema

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = { 
    Slash: {
        data: new SlashCommandBuilder()
            .setName('developer')
            .setDescription('Developer utilities')
            .addSubcommand(subcommand => 
                subcommand
                    .setName('args')
                    .setDescription('Handle developer arguments')
                    .addStringOption(option => 
                        option.setName('type')
                            .setDescription('Select a type')
                            .setRequired(true)
                            .addChoices([
                                { name: 'Appeal Restriction', value: 'option1' }
                            ])
                    )
                    .addChannelOption(option => 
                        option.setName('channel')
                            .setDescription('Select a channel')
                            .setRequired(true)
                    )
            ),
    },
};

module.exports.slashRun = async (interaction, client) => {
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (ownerIds.includes(interaction.user.id)) {
        // Bypass all permission checks for owners
    } else {
        const settings = await Settings.findOne({ guildId: interaction.guild.id });
        const staffRoleId = settings?.staffRoleId;

        if (!staffRoleId) {
            return interaction.reply({
                content: '❌ The staff role is not set up. Please use `/setstaffrole` to configure it.',
                ephemeral: true,
            });
        }

        if (!interaction.member.roles.cache.has(staffRoleId)) {
            return interaction.reply({
                content: '❌ You do not have the required staff role to use this command.',
                ephemeral: true,
            });
        }
    }

    // Defer the interaction to keep it valid while processing
    if (!interaction.deferred && !interaction.replied) {
        await interaction.deferReply({ ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();
    
    if (subcommand === 'args') {
        const type = interaction.options.getString('type');
        const channel = interaction.options.getChannel('channel');

        const typeNames = {
            option1: 'Appeal Restriction'
        };

        const capitalizedType = typeNames[type] || 'Unknown Type';

        if (type === 'option1') {
            const embed = client.embed()
                .setTitle('Appeal Restriction')
                .setDescription(`**You've been restricted from ${interaction.guild.name}**\nIf you believe this is a mistake or want to appeal, click "Ban Information" to check the reason and appeal eligibility. Then, click "Appeal Ban" to submit your request for review`)
                .setColor('Red');

            const row1 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('appeal_ban')
                        .setLabel('Appeal Ban')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('ban_information')
                        .setLabel('Ban Information')
                        .setStyle(ButtonStyle.Secondary)
                );

            await channel.send({ embeds: [embed], components: [row1] });

            // Logic to restore roles after appeal is accepted
            const restrictionData = await RestrictionUserData.findOne({ userId: interaction.user.id, isActive: true });

            if (restrictionData) {
                const previousRoles = restrictionData.previousRoles || []; // Fetch previous roles
                const memberRole = settings?.memberRole; // Fetch the member role from settings
                const member = interaction.guild.members.cache.get(interaction.user.id);

                if (member) {
                    // Restore previous roles
                    for (const roleId of previousRoles) {
                        const role = interaction.guild.roles.cache.get(roleId);
                        if (role) {
                            try {
                                await member.roles.add(role);
                            } catch (err) {
                                console.error(`Failed to add previous role ${roleId} to user ${interaction.user.id}:`, err);
                            }
                        }
                    }

                    // Assign the member role if specified
                    if (memberRole) {
                        const role = interaction.guild.roles.cache.get(memberRole);
                        if (role) {
                            try {
                                await member.roles.add(role);
                            } catch (err) {
                                console.error(`Failed to add member role ${memberRole} to user ${interaction.user.id}:`, err);
                            }
                        }
                    }
                }

                // Mark the restriction as inactive
                restrictionData.isActive = false;
                await restrictionData.save();
            }
        }

        // Edit the deferred reply with the final response
        await interaction.editReply({ content: `I've sent ${capitalizedType} to the channel <#${channel.id}>!` });
    }
};
