const Permissions = require('../../utils/checkPermissions.js');
const axios = require('axios');
const { MessageFlags } = require('discord.js');
const User = require('../../components/modals/User');
const { checkLocalBlacklist } = require('../../middleware/localBlacklist');


module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'interactionCreate'
};

module.exports.eventRun = async (interaction, client) => {
    try {
        // Handle autocomplete interactions FIRST (before blacklist checks)
        if (interaction.isAutocomplete()) {
            const command = client.application_commands.get(interaction.commandName);
            if (!command) return;
            try {
                await command.autocompleteRun(interaction, client);
            } catch (err) {
                client.emit('errorCreate', client, err, interaction.commandName, interaction);
            }
            return;
        }

        // Check local blacklist for all other interactions
        const localBlacklistCheck = await checkLocalBlacklist(interaction);
        if (localBlacklistCheck.isBlacklisted) {
            // GDPR blacklist messages are shown publicly, others are ephemeral
            const isPublic = localBlacklistCheck.type === 'gdpr';
            return await interaction.reply({
                content: localBlacklistCheck.message,
                flags: isPublic ? undefined : 64 // Only ephemeral for non-GDPR blacklists
            });
        }

        if (interaction.isChatInputCommand()) {
            const command = client.application_commands.get(interaction.commandName);
            if (!command) return;

            const { userPermissions, botPermissions, isDefaultCooldown, isPremiumCooldown } = command.conf.Slash;

            const userCooldownKey = `${interaction.user.id}-${interaction.commandName}`;
            const userPremiumStatus = await User.findOne({ userID: interaction.user.id });
            let cooldown = isDefaultCooldown;
            if (userPremiumStatus && userPremiumStatus.FLAGS.includes("PREMIUM") && isPremiumCooldown) {
                cooldown = isPremiumCooldown;
            }

            if (isDefaultCooldown) {
                // Initialize cooldowns Map if it doesn't exist
                if (!client.cooldowns) {
                    client.cooldowns = new Map();
                }

                const lastUsed = client.cooldowns.get(userCooldownKey);
                if (lastUsed && lastUsed > Date.now()) {
                    const timeLeft = (lastUsed - Date.now()) / 1000;
                    const embed = client.embed()
                        .setColor(client.config.discord.defaultColor)
                        .setTitle("Woahhh! Slow down there Mr. Speedy Gonzales!")
                        .setDescription(`You can run this command again <t:${Math.floor(lastUsed / 1000)}:R>`)
                        .addFields(
                            { name: "Default Cooldown", value: `The Default Cooldown is **${isDefaultCooldown / 1000} Seconds**`, inline: true },
                            { name: "Premium Cooldown", value: `The Premium Cooldown is **${isPremiumCooldown / 1000} Seconds**`, inline: true }
                        );
                    if (!interaction.replied && !interaction.deferred) {
                        return interaction.reply({ embeds: [embed] });
                    } else if (interaction.deferred) {
                        return interaction.followUp({ embeds: [embed] });
                    }
                }

                // Set cooldown
                const expirationTime = Date.now() + cooldown;
                client.cooldowns.set(userCooldownKey, expirationTime);

                // Clean up expired cooldowns after the cooldown period
                setTimeout(() => {
                    client.cooldowns.delete(userCooldownKey);
                }, cooldown);
            }

            if (userPermissions) {
                const missingUserPermissions = userPermissions.filter(perm => !interaction.member.permissions.has(Permissions[perm]));
                if (missingUserPermissions.length > 0) {
                    if (!interaction.replied && !interaction.deferred) {
                        return client.errNormal({
                            error: `You are missing the following permissions: ${missingUserPermissions.join(", ")}`,
                            type: "reply",
                        }, interaction);
                    } else if (interaction.deferred) {
                        return interaction.followUp({ embeds: [embed] });
                    }
                }
            }

            if (botPermissions) {
                const botMember = interaction.guild?.members.me;
                const missingBotPermissions = botPermissions.filter(perm => !botMember.permissions.has(Permissions[perm]));
                if (missingBotPermissions.length > 0) {
                    if (!interaction.replied && !interaction.deferred) {
                        return client.errNormal({
                            error: `I am missing the following permissions: ${missingBotPermissions.join(", ")}`,
                            type: "reply",
                        }, interaction);
                    } else if (interaction.deferred) {
                        return interaction.followUp({ embeds: [embed] });
                    }
                }
            }

            try {
                await command.slashRun(interaction, client);
            } catch (err) {
                client.emit('errorCreate', client, err, interaction.commandName, interaction);
            }
        } else if (interaction.isButton()) {
            const buttonID = interaction.customId;
            const buttonComponent = client.buttonComponents?.get(buttonID);
            if (buttonComponent) {
                try {
                    await buttonComponent.buttonRun(interaction, client);
                } catch (err) {
                    client.emit('errorCreate', client, err, interaction.commandName, interaction);
                }
            }
        } else if (interaction.isModalSubmit()) {
            const modalID = interaction.customId;
            const modalComponent = client.modalComponents?.get(modalID);
            if (modalComponent) {
                try {
                    await modalComponent.modalRun(interaction, client);
                } catch (err) {
                    client.emit('errorCreate', client, err, interaction.commandName, interaction);
                }
            }
        } else if (interaction.isStringSelectMenu()) {
            const selectMenuID = interaction.customId;
            const selectMenuComponent = client.selectMenuComponents?.get(selectMenuID);
            if (selectMenuComponent) {
                try {
                    await selectMenuComponent.selectMenuRun(interaction, client);
                } catch (err) {
                    client.emit('errorCreate', client, err, interaction.commandName, interaction);
                }
            }
        }
    } catch (err) {
        client.emit('errorCreate', client, err, interaction.commandName, interaction);
    }
};