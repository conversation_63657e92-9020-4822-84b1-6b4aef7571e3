const { SlashCommandBuilder } = require('discord.js');
const os = require('os');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('stats')
            .setDescription('View bot statistics'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const uptime = process.uptime();
        const totalMem = os.totalmem() / 1024 / 1024;
        const freeMem = os.freemem() / 1024 / 1024;

        const statsEmbed = client.embed()
            .setTitle('Bot Statistics')
            .addFields(
                { name: 'Servers', value: `${client.guilds.cache.size}`, inline: true },
                { name: 'Users', value: `${client.users.cache.size}`, inline: true },
                { name: 'Ping', value: `${Math.round(client.ws.ping)}ms`, inline: true },
                { name: 'Uptime', value: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`, inline: true },
                { name: 'Memory Usage', value: `${(totalMem - freeMem).toFixed(2)} MB / ${totalMem.toFixed(2)} MB`, inline: true }
            )
            .setColor('#5865F2')
            .setTimestamp();

        await interaction.editReply({ embeds: [statsEmbed] });
    } catch (error) {
        console.error('Error executing stats command:', error);

        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    ephemeral: true,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
