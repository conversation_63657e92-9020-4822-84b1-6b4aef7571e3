const normalizeBoolean = (value) => value === 'true';
require('dotenv').config(); 

module.exports = {
  client: {
    token: process.env.BOT_TOKEN || '',
    app_id: process.env.APP_ID || '',
  },
  plugins: ['example'],
  discord: {
    footer: `© Nexoria Development | 2024 - ${new Date().getFullYear()}`,
    botInvite:
      process.env.BOT_INVITE ||
      `https://discord.com/oauth2/authorize?&client_id=${process.env.APP_ID}&scope=applications.commands+bot&permissions=8`,
    serverInvite: process.env.SERVER_INVITE || 'https://discord.gg/3wx2eUrStN',
    defaultColour: process.env.DEFAULT_COLOUR || '#47b269',
  },
  commands: {
    prefix: process.env.COMMAND_PREFIX || '?',
    message_commands: normalizeBoolean(process.env.MESSAGE_COMMANDS || 'true'),
    application_commands: {
      chat_input: normalizeBoolean(process.env.CHAT_INPUT_COMMANDS || 'true'),
      user_context: normalizeBoolean(process.env.USER_CONTEXT_COMMANDS || 'true'),
      message_context: normalizeBoolean(process.env.MESSAGE_CONTEXT_COMMANDS || 'true'),
    },
  },
  components: {
    button_components: normalizeBoolean(process.env.BUTTON_COMPONENTS || 'true'),
    modal_components: normalizeBoolean(process.env.MODAL_COMPONENTS || 'true'),
    select_menu_components: normalizeBoolean(process.env.SELECT_MENU_COMPONENTS || 'true'),
  },
  mongoDB: {
    enabled: normalizeBoolean(process.env.MONGO_ENABLED || 'true'),
    mongoURI: process.env.MONGO_URI || '',
  },
};