const {
    EmbedBuilder
} = require('discord.js');

module.exports = (client) => {
    const createEmbedTemplate = () => {
        return new EmbedBuilder()
            .setAuthor({
                name: client.user.username,
                iconURL: client.user.avatarURL({
                    size: 1024
                })
            })
            .setFooter({
                text: client.config.discord.footer,
                iconURL: client.user.avatarURL({
                    size: 1024
                })
            })
            .setColor(client.config.discord.defaultColour)
            .setTimestamp();
    };

    client.templateEmbed = createEmbedTemplate;
    client.embed = createEmbedTemplate;

    client.createEmbed = async ({
        embed = createEmbedTemplate(),
        title,
        desc,
        color,
        image,
        thumbnail,
        fields,
        author,
        url,
        footer,
        content,
        components,
        type
    }, interaction) => {
        if (interaction.guild === undefined) interaction.guild = {
            id: "0"
        };

        if (title) embed.setTitle(title);
        if (desc) embed.setDescription(desc.length >= 2048 ? `${desc.substr(0, 2044)}...` : desc);
        if (image) embed.setImage(image);
        if (thumbnail) embed.setThumbnail(thumbnail);
        if (fields) embed.addFields(fields);
        if (author) embed.setAuthor(author);
        if (url) embed.setURL(url);
        if (footer) embed.setFooter({
            text: footer
        });
        if (color) embed.setColor(color);

        if (interaction.replied || interaction.deferred) {
            return;
        }

        return client.sendEmbed({
            embeds: [embed],
            content,
            components,
            type
        }, interaction);
    };

    client.sendEmbed = async ({
        embeds,
        content,
        components,
        type
    }, interaction) => {
        if (interaction.replied || interaction.deferred) {
            return;
        }

        const options = {
            embeds,
            content: content || null,
            components: components || [],
            fetchReply: true,
            ephemeral: type?.toLowerCase().includes('ephemeral')
        };

        switch (type?.toLowerCase()) {
            case 'edit':
                return interaction.edit(options);
            case 'editreply':
            case 'ephemeraledit':
                return interaction.editReply(options);
            case 'reply':
            case 'ephemeral':
                return interaction.reply(options);
            case 'update':
                return interaction.update(options);
            default:
                return interaction.send(options);
        }
    };

    client.errNormal = async ({
        embed = createEmbedTemplate(),
        error = "An error message is not provided",
        type,
        content,
        components
    }, interaction) => {
        if (interaction.replied || interaction.deferred) {
            return;
        }

        embed.setTitle('Error!')
            .addFields([{
                name: "💬 Error comment",
                value: `\`\`\`${error}\`\`\``
            }])
            .setColor('Red');

        return client.sendEmbed({
            embeds: [embed],
            content,
            components,
            type
        }, interaction);
    };
};