const crypto = require('crypto');
const ColorManager = require('./colorManager');
const Banner = require('./banner');

class LogManager {
    constructor() {
        this.wsServer = null;
        this.cleanupInterval = null;
        this.bannerShown = false;
        this.logs = []; 
    }

    setWebSocketServer(wsServer) {
        if (wsServer && typeof wsServer.broadcastNewLog === 'function' && typeof wsServer.notifyLogDeleted === 'function') {
            this.wsServer = wsServer;
        }
    }

    async initialize() {
        this.startCleanupSchedule();
    }

    startCleanupSchedule() {
        if (this.cleanupInterval) clearInterval(this.cleanupInterval);
        this.cleanupInterval = setInterval(() => {
            this.cleanOldLogs();
        }, 60 * 60 * 1000);
    }

    stripAnsiCodes(str) {
        return typeof str === 'string' ? str.replace(/\u001b\[\d+m/g, '') : String(str);
    }

    async addLog(category, message) {
        const timestamp = new Date().toISOString();
        const cleanMessage = this.stripAnsiCodes(message);
        const id = crypto.randomUUID();

        const logEntry = { id, category, message: cleanMessage, timestamp };
        this.logs.push(logEntry);

        if (this.wsServer) {
            try {
                this.wsServer.broadcastNewLog(logEntry);
            } catch (error) {
                console.error('Error broadcasting new log:', error);
            }
        }

        return logEntry;
    }

    async cleanOldLogs() {
        const twelveHoursAgo = new Date();
        twelveHoursAgo.setHours(twelveHoursAgo.getHours() - 12);
        this.logs = this.logs.filter(log => new Date(log.timestamp) >= twelveHoursAgo);
        return true;
    }

    async getLogs() {
        return this.logs.reduce((logsByCategory, log) => {
            if (!logsByCategory[log.category]) logsByCategory[log.category] = [];
            logsByCategory[log.category].push(log);
            return logsByCategory;
        }, {});
    }

    async deleteLog(id) {
        const index = this.logs.findIndex(log => log.id === id);
        if (index !== -1) {
            this.logs.splice(index, 1);
            if (this.wsServer) {
                try {
                    this.wsServer.notifyLogDeleted(id);
                } catch (error) {
                    console.error('Error notifying log deletion:', error);
                }
            }
            return true;
        }
        return false;
    }

    async clearLogs() {
        this.logs = [];
        return true;
    }

    showStartupBanner() {}

    divider() {
        console.log(ColorManager.colors.brackets + "────────────────────────────────────────────────────────────" + ColorManager.colors.reset);
    }

    _logWithCategory(category, message) {
        const formattedMessage = ColorManager.formatLogMessage(category, message);
        console.log(formattedMessage);
        this.addLog(category, message);
    }

    startup(message) { this._logWithCategory('Startup', message); }
    info(message) { this._logWithCategory('Info', message); }
    error(message) { this._logWithCategory('Error', message); }
    warn(message) { this._logWithCategory('Warning', message); }
    success(message) { this._logWithCategory('Success', message); }
    system(message) { this._logWithCategory('System', message); }
    debug(message) { this._logWithCategory('Debug', message); }
    command(message) { this._logWithCategory('Command', message); }
    event(message) { this._logWithCategory('Event', message); }
    database(message) { this._logWithCategory('Database', message); }
    api(message) { this._logWithCategory('API', message); }
    component(message) { this._logWithCategory('Component', message); }
    dashboard(message) { this._logWithCategory('Dashboard', message); }
    cache(message) { this._logWithCategory('Cache', message); }
    interaction(message) { this._logWithCategory('Interaction', message); }
    prefix(message) { this._logWithCategory('Prefix', message); }
    count(message) { this._logWithCategory('Count', message); }
}

module.exports = new LogManager();