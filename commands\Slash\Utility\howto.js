const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    conf: {
        Slash: {
            enabled: true,
            userPermissions: [],
            botPermissions: [],
        },
    },
    help: {
        Slash: {
            data: new SlashCommandBuilder()
                .setName('howto')
                .setDescription('Learn how to use the Star-Gate bot and get started!'),
        },
    },
    slashRun: async (interaction) => {
        await interaction.reply({
            embeds: [
                {
                    title: 'How to Use Star-Gate Bot',
                    description: `Welcome to Star-Gate! Here’s how to get started and what you can do with this bot.\n\n**Getting Started:**\n- Make sure you have the right permissions to use the bot’s commands.\n- Use "/help" to see a list of all available commands.\n- Some commands may require special roles (like staff or owner).\n\n**What This Bot Does:**\n- Star-Gate provides utility, moderation, and information commands for your server.\n- It helps manage restrictions, roles, and provides useful server info.\n- Owner and staff commands allow for advanced server management.\n\n**Basic Workflow:**\n1. Use "/settings" to configure the bot for your server.\n2. Use moderation commands (like "/restriction") to manage users.\n3. Use utility commands (like "/clear", "/invite", "/credits") for various server tasks.\n4. Check user or server info with commands like "/info" or "/stats".\n\n**Need More Help?**\n- Use "/help" for a full list of commands.\n- Contact a server admin or use the support command if available.\n\nEnjoy using Star-Gate!`,
                    color: 0x00AE86
                },
                {
                    title: 'How to Restrict a User',
                    description: `- Only users with the required staff or moderation role can restrict others.\n- Use the "/restriction" command to restrict a user.\n\n**Steps to restrict a user:**\n1. Make sure you have the staff role set up (use "/setstaffrole" if needed).\n2. Run "/restriction" and select the user you want to restrict.\n3. Provide a reason if prompted.\n4. The bot will apply the restriction and log the action.\n\nTo view restriction history, use "/restrictedHistory".`,
                    color: 0xE74C3C
                },
                {
                    title: 'Add Star-Gate Bot & Support',
                    description: `**Add the Bot to Your Server:**\n[Invite Star-Gate Bot](https://discord.com/oauth2/authorize?client_id=1361507637606486027&scope=bot+applications.commands&permissions=8)\n\n**Need Help or Support?**\nJoin our support server: [Support Server](https://discord.nexoriadevelopment.com)`,
                    color: 0x5865F2
                }
            ],
            ephemeral: false
        });
    },
};
