const { WebhookClient, EmbedBuilder } = require('discord.js');

let gdprWebhook = null;

// Initialize GDPR webhook client
function initializeGDPRLogger() {
    const webhookUrl = process.env.GDPR_WEBHOOK_URL;
    if (webhookUrl && webhookUrl !== 'your_gdpr_webhook_url_here') {
        try {
            gdprWebhook = new WebhookClient({ url: webhookUrl });
            console.log('✅ GDPR webhook logger initialized');
        } catch (error) {
            console.error('❌ Failed to initialize GDPR webhook:', error);
        }
    } else {
        console.log('⚠️ GDPR webhook URL not configured');
    }
}

// Send GDPR log to webhook for legal compliance
async function sendGDPRLog(type, data) {
    if (!gdprWebhook) return;

    try {
        const embed = new EmbedBuilder()
            .setTimestamp()
            .setFooter({ text: 'GDPR Compliance System - Legal Audit Log' });

        switch (type) {
            case 'GDPR_BLACKLIST_APPLIED':
                embed
                    .setTitle('🔒 GDPR Blacklist Applied - Legal Compliance')
                    .setColor(0x800080)
                    .setDescription('**LEGAL NOTICE: GDPR Data Deletion Request Processed**')
                    .addFields(
                        { name: '👤 Target User', value: `${data.targetUser}\nID: \`${data.targetUserId}\``, inline: true },
                        { name: '⚖️ Legal Basis', value: 'GDPR Article 17\n(Right to Erasure)', inline: true },
                        { name: '📅 Processing Date', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '👨‍💼 Processed By', value: `${data.processedBy}\nID: \`${data.processedById}\``, inline: true },
                        { name: '📋 Request Details', value: data.requestDetails || 'GDPR data deletion request', inline: true },
                        { name: '🚨 Compliance Status', value: '✅ COMPLIANT\n🔒 PERMANENT', inline: true },
                        { name: '📊 Database Log Entry', value: `**User ID:** \`${data.databaseLog.user_id}\`\n**Reason:** \`${data.databaseLog.reason}\`\n**Flags:** \`${data.databaseLog.flags.join(', ')}\`\n**Timestamp:** \`${data.databaseLog.timestamp}\`\n**Issuer:** \`${data.databaseLog.Issuer}\``, inline: false },
                        { name: '⚠️ Legal Implications', value: '• User permanently blocked from all bot services\n• Data collection prohibited\n• Cannot be reversed or modified\n• Complies with GDPR Article 17', inline: false },
                        { name: '📞 Audit Trail', value: `**Action:** GDPR Blacklist Applied\n**Reversible:** ❌ NO\n**Legal Compliance:** ✅ YES\n**Data Retention:** Audit logs only`, inline: false }
                    );
                break;

            case 'GDPR_BLACKLIST_ATTEMPTED':
                embed
                    .setTitle('⚠️ GDPR Blacklist Attempt - Legal Notice')
                    .setColor(0xFFA500)
                    .setDescription('**LEGAL NOTICE: GDPR Blacklist Attempt (Failed)**')
                    .addFields(
                        { name: '👤 Target User', value: `${data.targetUser}\nID: \`${data.targetUserId}\``, inline: true },
                        { name: '👨‍💼 Attempted By', value: `${data.attemptedBy}\nID: \`${data.attemptedById}\``, inline: true },
                        { name: '❌ Failure Reason', value: data.failureReason, inline: true },
                        { name: '📅 Attempt Date', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '📋 Request Details', value: data.requestDetails || 'GDPR data deletion request', inline: true },
                        { name: '🚨 Status', value: '❌ FAILED\n⚠️ REQUIRES ATTENTION', inline: true },
                        { name: '⚖️ Legal Notice', value: 'Failed GDPR blacklist attempts must be reviewed for compliance. Manual intervention may be required.', inline: false }
                    );
                break;

            case 'GDPR_LOG_ACCESSED':
                embed
                    .setTitle('📋 GDPR Logs Accessed - Audit Notice')
                    .setColor(0x3498DB)
                    .setDescription('**AUDIT NOTICE: GDPR Logs Accessed**')
                    .addFields(
                        { name: '👨‍💼 Accessed By', value: `${data.accessedBy}\nID: \`${data.accessedById}\``, inline: true },
                        { name: '📅 Access Date', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '🔍 Query Type', value: data.queryType, inline: true },
                        { name: '📊 Records Accessed', value: `${data.recordCount} log entries`, inline: true },
                        { name: '👤 Target User', value: data.targetUser ? `${data.targetUser}\nID: \`${data.targetUserId}\`` : 'All Users', inline: true },
                        { name: '📋 Access Reason', value: data.accessReason || 'GDPR audit/compliance check', inline: true },
                        { name: '⚖️ Legal Compliance', value: 'GDPR log access is monitored for compliance and audit purposes.', inline: false }
                    );
                break;

            case 'GDPR_COMPLIANCE_CHECK':
                embed
                    .setTitle('🔍 GDPR Compliance Check - Legal Audit')
                    .setColor(0x2ECC71)
                    .setDescription('**COMPLIANCE AUDIT: GDPR Status Verification**')
                    .addFields(
                        { name: '👤 User Checked', value: `${data.targetUser}\nID: \`${data.targetUserId}\``, inline: true },
                        { name: '👨‍💼 Checked By', value: `${data.checkedBy}\nID: \`${data.checkedById}\``, inline: true },
                        { name: '📅 Check Date', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '🔒 GDPR Status', value: data.gdprStatus, inline: true },
                        { name: '📋 Check Reason', value: data.checkReason || 'Compliance verification', inline: true },
                        { name: '⚖️ Compliance', value: data.complianceStatus, inline: true },
                        { name: '📊 Audit Trail', value: 'GDPR status checks are logged for legal compliance and audit purposes.', inline: false }
                    );
                break;

            case 'GDPR_USER_RESTRICTED':
                embed
                    .setTitle('🔒 GDPR Protected User Restricted')
                    .setColor(0x9B59B6)
                    .setDescription('**GDPR COMPLIANCE: Protected User Restriction Applied**')
                    .addFields(
                        { name: '🆔 User ID', value: `\`${data.userId}\``, inline: true },
                        { name: '👨‍💼 Restricted By', value: `${data.restrictedBy}\nID: \`${data.restrictedById}\``, inline: true },
                        { name: '📅 Restriction Date', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '🏠 Server', value: `${data.serverName}\nID: \`${data.serverId}\``, inline: true },
                        { name: '📋 Reason', value: data.reason, inline: true },
                        { name: '🔒 Data Protection', value: 'GDPR Compliant\nMinimal Data Logged', inline: true },
                        { name: '⚠️ GDPR Notice', value: 'This user is GDPR protected. Only User ID and minimal restriction data is logged for compliance purposes.', inline: false },
                        { name: '📊 Database Entry', value: 'Limited data stored:\n• User ID only\n• Server marked as GDPR_PROTECTED\n• Reason marked as GDPR_PROTECTED\n• No personal data retained', inline: false }
                    );
                break;

            default:
                embed
                    .setTitle('📝 GDPR System Log')
                    .setColor(0x95A5A6)
                    .setDescription(`**GDPR Event:** ${type}`)
                    .addFields({ name: 'Data', value: JSON.stringify(data, null, 2).substring(0, 1000), inline: false });
        }

        // Add legal disclaimer footer
        embed.addFields({
            name: '⚖️ Legal Disclaimer',
            value: 'This log is maintained for GDPR compliance and legal audit purposes. All GDPR-related actions are permanent and cannot be reversed in accordance with data protection regulations.',
            inline: false
        });

        await gdprWebhook.send({ embeds: [embed] });
    } catch (error) {
        console.error('❌ Failed to send GDPR log to webhook:', error);
    }
}

module.exports = {
    initializeGDPRLogger,
    sendGDPRLog
};
