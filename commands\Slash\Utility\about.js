const { SlashCommandBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('about') 
            .setDescription('Learn more about this bot'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Defer the reply to keep the interaction alive
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const embed = client.embed()
            .setTitle('About Nexoria Development')
            .setDescription('Nexoria Development is a powerful moderation bot designed to help server admins manage restrictions and appeals efficiently.')
            .addFields(
                { name: 'Features', value: '- Restrict roles for users\n- Manage appeal restrictions\n- Log important actions\n- Developer utilities like server management' },
                { name: 'Commands', value: 'Use `/help` to see a list of available commands.' },
                { name: 'Support', value: 'Need help? Contact the bot developers or visit our support server.' }
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setColor('#5865F2');

        // Edit the deferred reply with the embed
        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('Error executing about command:', error);

        // Handle errors gracefully
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: 'An error occurred while processing your request. Please try again later.',
                ephemeral: true,
            });
        }
    }
};
