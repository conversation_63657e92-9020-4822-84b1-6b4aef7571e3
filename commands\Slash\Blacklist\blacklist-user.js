const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { addToLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('blacklist-user')
            .setDescription('Blacklist a user from using the bot (Developer only)')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to blacklist')
                    .setRequired(true))
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('Reason for blacklisting')
                    .setRequired(true))
            .addBooleanOption(option =>
                option.setName('global')
                    .setDescription('Apply blacklist globally (all servers)')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const targetUser = interaction.options.getUser('user');
    const reason = interaction.options.getString('reason');
    const isGlobal = interaction.options.getBoolean('global') || false;
    const guildId = isGlobal ? 'global' : interaction.guild.id;

    // Prevent blacklisting other developers
    if (isDeveloper(targetUser.id)) {
        return interaction.reply({
            content: '❌ Cannot blacklist other developers.',
            ephemeral: true
        });
    }

    // Prevent self-blacklisting
    if (targetUser.id === interaction.user.id) {
        return interaction.reply({
            content: '❌ You cannot blacklist yourself.',
            ephemeral: true
        });
    }

    try {
        const result = await addToLocalBlacklist(
            targetUser.id,
            guildId,
            reason,
            interaction.user.id,
            'user'
        );

        if (result.success) {
            // Log the blacklist action
            sendBlacklistLog('USER_BLACKLISTED', {
                targetUser: targetUser.tag,
                targetUserId: targetUser.id,
                blacklistedBy: interaction.user.tag,
                blacklistedById: interaction.user.id,
                scope: isGlobal ? 'Global' : 'Server-specific',
                reason: reason
            });

            const embed = new EmbedBuilder()
                .setTitle('✅ User Blacklisted')
                .setColor(0xFF0000)
                .addFields(
                    { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: true },
                    { name: 'Reason', value: reason, inline: true },
                    { name: 'Scope', value: isGlobal ? 'Global' : 'This Server', inline: true },
                    { name: 'Blacklisted by', value: `${interaction.user}`, inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed] });
        } else {
            await interaction.reply({
                content: `❌ Failed to blacklist user: ${result.error}`,
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error blacklisting user:', error);
        await interaction.reply({
            content: '❌ An error occurred while blacklisting the user.',
            ephemeral: true
        });
    }
};
