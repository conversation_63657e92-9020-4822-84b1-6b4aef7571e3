const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { WebhookClient } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

const suggestionWebhookUrl = process.env.suggestionWebhook; // Webhook URL for suggestions
const suggestionWebhookClient = suggestionWebhookUrl ? new WebhookClient({ url: suggestionWebhookUrl }) : null;

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('suggestion')
            .setDescription('Submit a suggestion for the bot or server')
            .addStringOption(option =>
                option.setName('content')
                    .setDescription('Your suggestion')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        const suggestion = interaction.options.getString('content');

        if (!suggestionWebhookClient) {
            return interaction.reply({
                content: 'The suggestion system is not configured. Please contact the bot owner.',
                ephemeral: true,
            });
        }

        const embed = new EmbedBuilder()
            .setTitle('New Suggestion')
            .setDescription(suggestion)
            .addFields(
                { name: 'User', value: `<@${interaction.user.id}> (${interaction.user.tag})`, inline: true },
                { name: 'Server', value: `${interaction.guild.name} (${interaction.guild.id})`, inline: true }
            )
            .setColor('#5865F2')
            .setTimestamp();

        await suggestionWebhookClient.send({ embeds: [embed] });

        await interaction.reply({
            content: 'Thank you for your suggestion! It has been sent to the bot owner.',
            ephemeral: true,
        });
    } catch (error) {
        console.error('Error executing suggestion command:', error);
        await interaction.reply({
            content: 'An error occurred while processing your suggestion. Please try again later.',
            ephemeral: true,
        });
    }
};
