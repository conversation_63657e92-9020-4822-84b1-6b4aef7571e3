const { <PERSON>lash<PERSON><PERSON>mandB<PERSON>er, <PERSON>bed<PERSON><PERSON>er, AuditLogEvent } = require('discord.js');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ["ManageGuild"],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('viewhistory')
            .setDescription('View the moderation history of a user')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to view moderation history for')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction, client) => {
    const user = interaction.options.getUser('user');
    const guild = interaction.guild;

    try {
        // Check if user is GDPR protected
        const LocalBlacklist = require('../../../models/LocalBlacklist');
        const gdprBlacklist = await LocalBlacklist.findOne({
            userId: user.id,
            guildId: 'global',
            type: 'gdpr'
        });

        if (gdprBlacklist) {
            const embed = new EmbedBuilder()
                .setTitle('🔒 GDPR Protected User')
                .setDescription('This user is protected under GDPR regulations')
                .setColor('#800080') // Purple for GDPR
                .addFields(
                    { name: '🔒 Status', value: 'Protected under GDPR', inline: true },
                    { name: '📋 Information', value: 'User history is protected', inline: true },
                    { name: '⚖️ Compliance', value: 'Data access restricted', inline: true }
                )
                .setFooter({ text: 'GDPR Data Protection' })
                .setTimestamp();

            return interaction.reply({ embeds: [embed] });
        }
        const member = await guild.members.fetch(user.id).catch(() => null);
        const ban = await guild.bans.fetch(user.id).catch(() => null);
        let timeoutStatus = 'N/A';

        if (member?.communicationDisabledUntilTimestamp && member.communicationDisabledUntilTimestamp > Date.now()) {
            timeoutStatus = `Timed out until <t:${Math.floor(member.communicationDisabledUntilTimestamp / 1000)}:F>`;
        }

        const banStatus = ban
            ? `Banned!\nReason: ${ban.reason || 'No reason given'}`
            : 'N/A';

        const auditLogs = await guild.fetchAuditLogs({ limit: 100 });
        const actionNames = {
            [AuditLogEvent.MemberBanAdd]: 'Ban',
            [AuditLogEvent.MemberKick]: 'Kick',
            [AuditLogEvent.MemberUpdate]: 'Timeout',
        };

        const counts = { TIMEOUT: 0, BAN: 0, KICK: 0 };
        const formattedActions = auditLogs.entries
            .filter(entry => {
                const isTarget = entry.target?.id === user.id;
                if (!isTarget) return false;
                if (
                    entry.action === AuditLogEvent.MemberBanAdd ||
                    entry.action === AuditLogEvent.MemberKick
                ) {
                    return true;
                }
                if (entry.action === AuditLogEvent.MemberUpdate) {
                    return entry.changes?.some(c => c.key === 'communication_disabled_until');
                }
                return false;
            })
            .map(entry => {
                const mod = `<@${entry.executor?.id || 'Unknown'}>`;
                const reason = entry.reason || 'No reason';
                const date = new Date(entry.createdTimestamp).toLocaleString();
                let actionLabel = actionNames[entry.action] || 'Unknown';
                const uniqueCode = entry.extra?.uniqueCode || 'N/A'; // Add unique code if available
                if (entry.action === AuditLogEvent.MemberBanAdd) {
                    counts.BAN++;
                } else if (
                    entry.action === AuditLogEvent.MemberUpdate &&
                    entry.changes?.some(c => c.key === 'communication_disabled_until')
                ) {
                    counts.TIMEOUT++;
                    actionLabel = 'Timeout';
                }
                return `**${actionLabel}**\n• Staff: ${mod}\n• Reason: ${reason}\n• Date: ${date}\n• Unique Code: ${uniqueCode}`;
            });

        const summary = `**Timeouts:** ${counts.TIMEOUT}\n**Bans:** ${counts.BAN}`;
        const embed = new EmbedBuilder()
            .setTitle('Moderation History')
            .setDescription(`Moderation history for <@${user.id}>`) 
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setColor('#ff5555')
            .addFields(
                { name: 'Timeout Status', value: timeoutStatus, inline: true },
                { name: 'Ban Status', value: banStatus, inline: true },
                { name: 'Action Summary', value: summary },
                { name: 'Recent Actions', value: formattedActions.length ? formattedActions.slice(0, 3).join('\n\n') : 'No recent mod actions found.' }
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'} | Command made by Richy` })
            .setTimestamp();

        // Send the embed as a public message
        await interaction.reply({
            embeds: [embed],
        });
    } catch (err) {
        console.error('Error in viewHistory.js:', err);
        await interaction.reply({
            content: 'Failed to fetch moderation history.',
            ephemeral: true,
        });
    }
};
