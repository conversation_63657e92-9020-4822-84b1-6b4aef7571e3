# 🚫 Advanced Discord Bot Blacklist System

A comprehensive, developer-only blacklist system with webhook logging, dual-layer protection, and automatic enforcement.

## 📋 Table of Contents
- [Features](#features)
- [Installation](#installation)
- [File Structure](#file-structure)
- [Environment Variables](#environment-variables)
- [Commands](#commands)
- [Webhook Logging](#webhook-logging)
- [Usage Examples](#usage-examples)
- [Integration Guide](#integration-guide)

---

## ✨ Features

### 🔒 **Security Features**
- **Developer-Only Access**: Only users in `DEVELOPER_IDS` can manage blacklists
- **Dual-Layer Protection**: Supports both admin bot integration and local blacklists
- **Automatic Enforcement**: Blocks all interactions and message processing
- **Auto-Leave**: Automatically leaves blacklisted guilds on join/blacklist

### 📊 **Scope Options**
- **User Blacklisting**: Server-specific or global scope
- **Guild Blacklisting**: Complete server blacklisting with auto-leave
- **Developer Bypass**: Developers bypass all blacklist checks (no logging)

### 🔍 **Monitoring & Logging**
- **Webhook Integration**: All actions logged to Discord webhook
- **Detailed Audit Trail**: Complete tracking of all blacklist activities
- **Real-time Notifications**: Instant alerts for blacklist events
- **Debug Information**: Console logging for troubleshooting

### 🛡️ **Safety Measures**
- **Self-Protection**: Cannot blacklist other developers or yourself
- **Guild Protection**: Cannot blacklist current guild without confirmation
- **Error Handling**: Graceful failure with user-friendly messages
- **Support Integration**: Blacklist messages include support server links

---

## 🚀 Installation

### Step 1: Install Dependencies
```bash
npm install mongodb discord.js
```

### Step 2: Create Directory Structure
```
your-bot/
├── models/
│   └── LocalBlacklist.js
├── middleware/
│   └── localBlacklist.js
├── utils/
│   └── blacklistLogger.js
├── commands/
│   └── blacklist/
│       ├── blacklist-user.js
│       ├── unblacklist-user.js
│       ├── blacklist-guild.js
│       ├── unblacklist-guild.js
│       ├── blacklist-list.js
│       ├── blacklist-check.js
│       └── force-leave.js
└── events/
    ├── interactionCreate.js
    ├── messageCreate.js (optional)
    └── guildCreate.js
```

### Step 3: Environment Variables
Add to your `.env` file:
```env
# Developer Configuration
DEVELOPER_IDS=123456789012345678,987654321098765432

# Support Server
SUPPORT_SERVER_URL=https://discord.gg/your-support-server

# Webhook Logging
BLACKLIST_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url_here

# MongoDB (if using admin bot integration)
MONGODB_URI=************************:port/database?authSource=admin
BOT_NAME=your_bot_name
```

---

## 📁 File Structure

### Required Files:
1. **`models/LocalBlacklist.js`** - Database model
2. **`middleware/localBlacklist.js`** - Core blacklist logic
3. **`utils/blacklistLogger.js`** - Webhook logging system
4. **`commands/blacklist/*.js`** - Management commands (7 files)
5. **`events/interactionCreate.js`** - Interaction handler with blacklist checks
6. **`events/guildCreate.js`** - Auto-leave for blacklisted guilds
7. **`events/messageCreate.js`** - Optional: XP/message processing protection

---

## ⚙️ Environment Variables

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `DEVELOPER_IDS` | ✅ | Comma-separated Discord user IDs | `123456789,987654321` |
| `SUPPORT_SERVER_URL` | ✅ | Discord invite for support | `https://discord.gg/support` |
| `BLACKLIST_WEBHOOK_URL` | ✅ | Discord webhook for logging | `https://discord.com/api/webhooks/...` |
| `MONGODB_URI` | ⚠️ | MongoDB connection (if using admin bot) | `mongodb://...` |
| `BOT_NAME` | ⚠️ | Unique bot identifier (if using admin bot) | `my_bot` |

---

## 🎮 Commands

### User Management
- **`/blacklist-user`** - Add user to blacklist (server/global scope)
- **`/unblacklist-user`** - Remove user from blacklist

### Guild Management  
- **`/blacklist-guild`** - Add guild to blacklist (auto-leaves if present)
- **`/unblacklist-guild`** - Remove guild from blacklist

### Utility Commands
- **`/blacklist-list`** - View all blacklist entries (users/guilds/all)
- **`/blacklist-check`** - Check specific user/guild blacklist status
- **`/force-leave`** - Force bot to leave a specific guild

### Testing
- **`/test-blacklist`** - Simple test command to verify blacklist functionality

---

## 📊 Webhook Logging

### Events Logged:
- 🔍 **User Blacklist Check** - Every blacklist verification
- 🚫 **User Blocked** - When blacklisted users are denied
- 🏠 **Guild Joined** - New guild joins
- 🚫 **Left Blacklisted Guild** - Auto-leave events
- 🚪 **Force Left Guild** - Manual guild departures
- 🚫 **User/Guild Blacklisted** - Blacklist additions
- ✅ **User/Guild Unblacklisted** - Blacklist removals

### Webhook Features:
- **Rich Embeds** with color coding
- **Detailed Information** (user/guild IDs, reasons, timestamps)
- **Action Context** (who performed action, scope, etc.)
- **Real-time Notifications** for immediate awareness

---

## 💡 Usage Examples

### Blacklist a Spammer (Server-Specific)
```
/blacklist-user user:@SpammerUser reason:"Excessive spam in channels" global:false
```

### Blacklist a User Globally
```
/blacklist-user user:@TrollUser reason:"Harassment across multiple servers" global:true
```

### Blacklist a Problematic Server
```
/blacklist-guild guild_id:123456789012345678 reason:"Terms of Service violation"
```

### Check if User is Blacklisted
```
/blacklist-check user target:@SuspiciousUser
```

### View All Blacklisted Users
```
/blacklist-list type:users global:false
```

### Remove User from Global Blacklist
```
/unblacklist-user user:@ReformedUser global:true
```

---

## 🔧 Integration Guide

### Step 1: Initialize in Main Bot File
```javascript
const { initializeBlacklistLogger } = require('./utils/blacklistLogger');

client.once('ready', async () => {
    // ... other initialization
    initializeBlacklistLogger();
});
```

### Step 2: Update Interaction Handler
```javascript
const { checkLocalBlacklist } = require('../middleware/localBlacklist');

// In your interactionCreate event:
const localBlacklistCheck = await checkLocalBlacklist(interaction);
if (localBlacklistCheck.isBlacklisted) {
    return await interaction.reply({
        content: localBlacklistCheck.message,
        flags: 64 // MessageFlags.Ephemeral
    });
}
```

### Step 3: Deploy Commands
```bash
node deploy-commands.js
```

### Step 4: Test the System
1. Use `/test-blacklist` to verify basic functionality
2. Blacklist a test user with `/blacklist-user`
3. Have that user try `/test-blacklist` (should be blocked)
4. Check webhook for logged events

---

## 🛠️ Customization Options

### Modify Blacklist Messages
Edit the `message` property in `middleware/localBlacklist.js`:
```javascript
message: `❌ **You are blacklisted from using this bot.**\n\n**Reason:** ${userBlacklist.reason}\n**Blacklisted by:** <@${userBlacklist.blacklistedBy}>\n\n🔗 **Need help?** Join our support server: ${supportServer}`
```

### Add Custom Webhook Events
Add new cases to `utils/blacklistLogger.js`:
```javascript
case 'CUSTOM_EVENT':
    embed
        .setTitle('🔔 Custom Event')
        .setColor(0x3498DB)
        .addFields(/* your fields */);
    break;
```

### Extend Database Model
Modify `models/LocalBlacklist.js` to add fields:
```javascript
const localBlacklistSchema = new mongoose.Schema({
    // existing fields...
    customField: { type: String, default: null },
    expiresAt: { type: Date, default: null }
});
```

---

## 🔍 Troubleshooting

### Common Issues:

**Commands not showing up:**
- Run `node deploy-commands.js`
- Check bot has slash command permissions

**Blacklisted users can still use commands:**
- Verify blacklist checks are in `interactionCreate.js`
- Check console logs for debug information
- Use `/blacklist-check` to verify user is actually blacklisted

**Webhook not logging:**
- Verify `BLACKLIST_WEBHOOK_URL` is correct
- Check webhook permissions in Discord
- Look for console errors about webhook failures

**Database errors:**
- Ensure MongoDB is running and accessible
- Check connection string format
- Verify database permissions

---

## 📝 Notes

- **Developer IDs**: Must be exact Discord user IDs (18-digit numbers)
- **Webhook URL**: Must be a valid Discord webhook URL
- **MongoDB**: Only required if using admin bot integration
- **Permissions**: Bot needs appropriate permissions in target guilds
- **Rate Limits**: Webhook logging respects Discord rate limits

---

## 🎯 Advanced Features

### Admin Bot Integration
If you want cross-bot blacklist management, implement the admin bot middleware alongside the local system for dual-layer protection.

### Temporary Blacklists
Extend the database model with `expiresAt` field and add cleanup logic.

### Blacklist Appeals
Create additional commands for managing appeals and temporary restrictions.

### Audit Logs
Implement additional logging for detailed audit trails and compliance.

---

---

## 📦 Complete Code Files

All the complete code files are provided in the `BLACKLIST_CODE_FILES.md` document. Simply copy and paste each file into your bot project following the directory structure above.

**🎉 Enjoy your new advanced blacklist system!**

This system provides enterprise-level blacklist management with comprehensive logging and safety features. Perfect for serious Discord bot operations requiring robust user management capabilities.

---

## 📞 Support

If you need help implementing this system:
1. Check the troubleshooting section above
2. Verify all environment variables are set correctly
3. Ensure all files are in the correct directories
4. Test with the `/test-blacklist` command first

**Happy coding!** 🚀
