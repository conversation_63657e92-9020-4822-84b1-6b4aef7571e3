const { <PERSON>lash<PERSON>ommandB<PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
}; 

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('help')
            .setDescription('Get help and support for using the bot'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Ensure the interaction is deferred only once
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const supportServer = process.env.support || 'https://discord.gg/Ured4CPqeD';
        const website = process.env.website || 'https://nexoriadevelopment.com';

        const embed = client.embed()
            .setTitle('Help & Support')
            .setDescription('Here are some resources to help you get started with Nexoria Development:')
            .addFields(
                { name: 'Commands', value: 'Use `/commands` to see a list of all available commands.' },
                { name: 'Support Server', value: `[Join Support Server](${supportServer})`, inline: true },
                { name: 'Website', value: `[Visit Website](${website})`, inline: true }
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setColor('#5865F2');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Support Server')
                    .setStyle(ButtonStyle.Link)
                    .setURL(supportServer),
                new ButtonBuilder()
                    .setLabel('Website')
                    .setStyle(ButtonStyle.Link)
                    .setURL(website)
            );

        // Edit the deferred reply with the embed
        await interaction.editReply({ embeds: [embed], components: [row] });
    } catch (error) {
        console.error('Error executing help command:', error);

        // Handle errors gracefully
        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    flags: MessageFlags.Ephemeral,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
