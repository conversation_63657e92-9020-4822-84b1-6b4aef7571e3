const { SlashCommandBuilder } = require('discord.js');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('test-blacklist')
            .setDescription('Test command to check if blacklist is working'),
    },
};

module.exports.slashRun = async (interaction) => {
    await interaction.reply({
        content: '✅ **Test successful!** You are not blacklisted and can use commands.',
        ephemeral: true
    });
};
