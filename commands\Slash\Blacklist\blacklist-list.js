const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('blacklist-list')
            .setDescription('View the current blacklist (Developer only)')
            .addStringOption(option =>
                option.setName('type')
                    .setDescription('Type of blacklist to view')
                    .setRequired(false)
                    .addChoices(
                        { name: 'Users', value: 'user' },
                        { name: 'Guilds', value: 'guild' },
                        { name: 'GDPR', value: 'gdpr' },
                        { name: 'All', value: 'all' }
                    ))
            .addBooleanOption(option =>
                option.setName('global')
                    .setDescription('View global blacklist')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const type = interaction.options.getString('type') || 'all';
    const isGlobal = interaction.options.getBoolean('global') || false;
    const guildId = isGlobal ? 'global' : interaction.guild?.id || 'global';

    try {
        const result = await getLocalBlacklist(guildId, type === 'all' ? null : type);

        if (!result.success) {
            return interaction.reply({
                content: `❌ Error retrieving blacklist: ${result.error}`,
                ephemeral: true
            });
        }

        const blacklist = result.blacklist;

        if (blacklist.length === 0) {
            return interaction.reply({
                content: `📝 No entries found in the ${isGlobal ? 'global' : 'server'} blacklist.`,
                ephemeral: true
            });
        }

        const embed = new EmbedBuilder()
            .setTitle(`🚫 Blacklist - ${isGlobal ? 'Global' : 'This Server'}`)
            .setColor(0xFF0000)
            .setFooter({ text: `Total entries: ${blacklist.length}` })
            .setTimestamp();

        let description = '';
        const users = blacklist.filter(entry => entry.type === 'user');
        const guilds = blacklist.filter(entry => entry.type === 'guild');
        const gdprUsers = blacklist.filter(entry => entry.type === 'gdpr');

        if (users.length > 0 && (type === 'all' || type === 'user')) {
            description += '**👤 Blacklisted Users:**\n';
            users.slice(0, 10).forEach((entry, index) => {
                description += `${index + 1}. <@${entry.userId}> - ${entry.reason}\n`;
            });
            if (users.length > 10) {
                description += `*...and ${users.length - 10} more users*\n`;
            }
            description += '\n';
        }

        if (guilds.length > 0 && (type === 'all' || type === 'guild')) {
            description += '**🏠 Blacklisted Guilds:**\n';
            guilds.slice(0, 10).forEach((entry, index) => {
                description += `${index + 1}. ${entry.userId} - ${entry.reason}\n`;
            });
            if (guilds.length > 10) {
                description += `*...and ${guilds.length - 10} more guilds*\n`;
            }
            description += '\n';
        }

        if (gdprUsers.length > 0 && (type === 'all' || type === 'gdpr')) {
            description += '**🔒 GDPR Blacklisted Users:**\n';
            gdprUsers.slice(0, 10).forEach((entry, index) => {
                description += `${index + 1}. <@${entry.userId}> - GDPR Data Deletion (PERMANENT)\n`;
            });
            if (gdprUsers.length > 10) {
                description += `*...and ${gdprUsers.length - 10} more GDPR users*\n`;
            }
        }

        embed.setDescription(description || 'No entries found.');

        await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
        console.error('Error listing blacklist:', error);
        await interaction.reply({
            content: '❌ An error occurred while retrieving the blacklist.',
            ephemeral: true
        });
    }
};
