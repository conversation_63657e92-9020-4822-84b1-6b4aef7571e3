const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Settings = require('../../../schemas/settings');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['ManageGuild'], // Require Manage Guild permission
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('viewsettings')
            .setDescription('View all configured roles and channels for the server'),
    },
};

module.exports.slashRun = async (interaction) => {
    try {
        // Fetch settings for the current guild
        const settings = await Settings.findOne({ guildId: interaction.guild.id });

        if (!settings) {
            return interaction.reply({
                content: 'No settings have been configured for this server.',
                ephemeral: true,
            });
        }

        const embed = new EmbedBuilder()
            .setTitle('Server Settings')
            .setDescription('Here are the current settings configured for this server:')
            .addFields(
                {
                    name: 'Staff Role',
                    value: settings.staffRoleId ? `<@&${settings.staffRoleId}>` : 'Not Set',
                    inline: true,
                },
                {
                    name: 'Restriction Role',
                    value: settings.restrictionRoleId ? `<@&${settings.restrictionRoleId}>` : 'Not Set',
                    inline: true,
                },
                {
                    name: 'Log Channel',
                    value: settings.logChannelId ? `<#${settings.logChannelId}>` : 'Not Set',
                    inline: true,
                },
                {
                    name: 'Appeal Log Channel',
                    value: settings.appealLogChannelId ? `<#${settings.appealLogChannelId}>` : 'Not Set',
                    inline: true,
                }
            )
            .setColor('#5865F2')
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Error executing viewsettings command:', error);
        await interaction.reply({
            content: 'An error occurred while fetching the server settings. Please try again later.',
            ephemeral: true,
        });
    }
};
