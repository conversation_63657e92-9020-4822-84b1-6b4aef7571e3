const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Settings = require('../../../schemas/settings'); // Import the settings schema
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // Only owners can use this command
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('announcement')
            .setDescription('Post an announcement in all servers (logging channels first, fallback to public channels)')
            .addStringOption(option =>
                option.setName('title')
                    .setDescription('The title of the announcement')
                    .setRequired(true)
            )
            .addStringOption(option =>
                option.setName('message')
                    .setDescription('The message content of the announcement')
                    .setRequired(true)
            )
            .addStringOption(option =>
                option.setName('footer')
                    .setDescription('The footer text for the announcement')
                    .setRequired(false)
            ),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Check if the user is an owner
        const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
        if (!ownerIds.includes(interaction.user.id)) {
            return interaction.reply({
                content: 'You do not have permission to use this command.',
                ephemeral: true,
            });
        }

        // Get the announcement details
        const title = interaction.options.getString('title');
        const message = interaction.options.getString('message');
        const footer = interaction.options.getString('footer') || 'Nexoria Development Announcement';

        // Create the announcement embed
        const embed = new EmbedBuilder()
            .setTitle(title)
            .setDescription(message)
            .setColor('#5865F2')
            .setFooter({ text: footer })
            .setTimestamp();

        // Get all guilds the bot is in
        const allGuilds = client.guilds.cache;
        let successCount = 0;
        let failureCount = 0;
        let logChannelCount = 0;
        let fallbackChannelCount = 0;

        for (const [guildId, guild] of allGuilds) {
            try {
                let targetChannel = null;
                let channelType = 'none';

                // First, try to get the logging channel from settings
                const guildSettings = await Settings.findOne({ guildId: guildId });
                if (guildSettings && guildSettings.logChannelId) {
                    targetChannel = guild.channels.cache.get(guildSettings.logChannelId);
                    if (targetChannel && targetChannel.permissionsFor(client.user).has(['SendMessages', 'EmbedLinks'])) {
                        channelType = 'logging';
                    } else {
                        targetChannel = null; // Channel doesn't exist or no permissions
                    }
                }

                // If no logging channel, find a suitable public channel
                if (!targetChannel) {
                    // Look for channels in order of preference
                    const channelPreferences = [
                        'announcements', 'general', 'main', 'chat', 'lobby', 'welcome'
                    ];

                    // First try to find a channel with preferred names
                    for (const preferredName of channelPreferences) {
                        targetChannel = guild.channels.cache.find(channel =>
                            channel.type === 0 && // Text channel
                            channel.name.toLowerCase().includes(preferredName) &&
                            channel.permissionsFor(client.user).has(['SendMessages', 'EmbedLinks']) &&
                            !channel.name.toLowerCase().includes('staff') &&
                            !channel.name.toLowerCase().includes('mod') &&
                            !channel.name.toLowerCase().includes('admin')
                        );
                        if (targetChannel) break;
                    }

                    // If still no channel found, get the first available text channel
                    if (!targetChannel) {
                        targetChannel = guild.channels.cache.find(channel =>
                            channel.type === 0 && // Text channel
                            channel.permissionsFor(client.user).has(['SendMessages', 'EmbedLinks']) &&
                            !channel.name.toLowerCase().includes('staff') &&
                            !channel.name.toLowerCase().includes('mod') &&
                            !channel.name.toLowerCase().includes('admin') &&
                            !channel.name.toLowerCase().includes('log')
                        );
                    }

                    if (targetChannel) {
                        channelType = 'fallback';
                    }
                }

                // Send the announcement if we found a suitable channel
                if (targetChannel) {
                    await targetChannel.send({ embeds: [embed] });
                    successCount++;

                    if (channelType === 'logging') {
                        logChannelCount++;
                    } else {
                        fallbackChannelCount++;
                    }

                    console.log(`📢 Announcement sent to ${guild.name} (${channelType}: #${targetChannel.name})`);
                } else {
                    failureCount++;
                    console.log(`❌ No suitable channel found in ${guild.name}`);
                }

            } catch (err) {
                console.error(`Failed to send announcement to guild ${guild.name} (${guildId}):`, err);
                failureCount++;
            }
        }

        // Reply to the interaction with detailed results
        const resultEmbed = new EmbedBuilder()
            .setTitle('📢 Announcement Results')
            .setColor(successCount > 0 ? '#00FF00' : '#FF0000')
            .addFields(
                { name: '✅ Total Success', value: `${successCount} servers`, inline: true },
                { name: '❌ Total Failed', value: `${failureCount} servers`, inline: true },
                { name: '📊 Total Servers', value: `${allGuilds.size} servers`, inline: true },
                { name: '📋 Logging Channels', value: `${logChannelCount} servers`, inline: true },
                { name: '🔄 Fallback Channels', value: `${fallbackChannelCount} servers`, inline: true },
                { name: '📈 Success Rate', value: `${((successCount / allGuilds.size) * 100).toFixed(1)}%`, inline: true }
            )
            .setFooter({ text: 'Announcement Distribution Complete' })
            .setTimestamp();

        await interaction.reply({
            embeds: [resultEmbed],
            ephemeral: true,
        });
    } catch (error) {
        console.error('Error executing announcement command:', error);
        await interaction.reply({
            content: 'An error occurred while processing your request. Please try again later.',
            ephemeral: true,
        });
    }
};
