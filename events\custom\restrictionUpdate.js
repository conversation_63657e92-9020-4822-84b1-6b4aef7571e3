

module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'restrictionUpdate'
};

module.exports.eventRun = async (guildId, client) => {
    try {
        // Only update if production stats are enabled
        if (process.env.SHOW_PRODUCTION_STATS !== 'true') return;

        // Check if client and guilds are properly initialized
        if (!client || !client.guilds || !client.guilds.cache) {
            return; // Silent fail - no logging needed
        }

        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            return; // Silent fail - no logging needed
        }

        // Get total restricted users from database (all servers) for bot status
        const RestrictionUserData = require('../../schemas/restrictionUserData');
        const totalRestrictedGlobal = await RestrictionUserData.countDocuments({
            isActive: true
        });

        // Get server-specific restricted user count from actual role members for voice channels
        const Settings = require('../../schemas/settings');
        const guildSettings = await Settings.findOne({ guildId: guildId });
        const restrictedRoleId = guildSettings?.restrictionRoleId;

        let serverRestrictedCount = 0;
        if (restrictedRoleId) {
            const restrictedRole = guild.roles.cache.get(restrictedRoleId);
            if (restrictedRole) {
                await guild.members.fetch(); // Ensure all members are cached
                serverRestrictedCount = restrictedRole.members.size;
            }
        }

        // Update voice channels that show restricted counts (server-specific)
        const restrictedChannels = guild.channels.cache.filter(channel =>
            channel.type === 2 && // GUILD_VOICE
            channel.name.toLowerCase().includes('restricted:')
        );

        for (const [, channel] of restrictedChannels) {
            try {
                const newName = `Restricted: ${serverRestrictedCount}`;
                if (channel.name !== newName) {
                    await channel.setName(newName);
                    // Silent update - no logging needed
                }
            } catch (error) {
                // Silent fail - no logging needed
            }
        }

        // Update bot status if not in maintenance or developer mode
        const isMaintenance = process.env.IS_MAINTENANCE === 'true';
        const isDeveloperMode = process.env.IS_DEVELOPER_MODE === 'true';

        if (!isMaintenance && !isDeveloperMode) {
            client.user.setPresence({
                activities: [{
                    name: `${totalRestrictedGlobal} Restricted Users`,
                    type: require('discord.js').ActivityType.Watching
                }],
                status: 'online'
            });
        }

    } catch (error) {
        // Silent error handling - no logging needed
    }
};
