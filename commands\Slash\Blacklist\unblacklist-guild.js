const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { removeFromLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('unblacklist-guild')
            .setDescription('Remove a guild from the blacklist (Developer only)')
            .addStringOption(option =>
                option.setName('guild_id')
                    .setDescription('The guild ID to unblacklist')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const guildId = interaction.options.getString('guild_id');

    try {
        const result = await removeFromLocalBlacklist(guildId, guildId, 'guild');

        if (result.success) {
            const targetGuild = interaction.client.guilds.cache.get(guildId);
            const guildName = targetGuild?.name || 'Unknown Guild';

            // Log the unblacklist action
            sendBlacklistLog('GUILD_UNBLACKLISTED', {
                guildName: guildName,
                guildId: guildId,
                removedBy: interaction.user.tag,
                removedById: interaction.user.id
            });

            const embed = new EmbedBuilder()
                .setTitle('✅ Guild Unblacklisted')
                .setColor(0x00FF00)
                .addFields(
                    { name: 'Guild', value: `${guildName} (${guildId})`, inline: true },
                    { name: 'Unblacklisted by', value: `${interaction.user}`, inline: true },
                    { name: 'Status', value: targetGuild ? 'Bot is currently in this guild' : 'Bot is not in this guild', inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed] });
        } else {
            await interaction.reply({
                content: '❌ Guild was not found in the blacklist.',
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error unblacklisting guild:', error);
        await interaction.reply({
            content: '❌ An error occurred while unblacklisting the guild.',
            ephemeral: true
        });
    }
};
