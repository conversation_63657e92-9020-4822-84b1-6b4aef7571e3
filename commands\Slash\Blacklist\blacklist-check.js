const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { checkLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('blacklist-check')
            .setDescription('Check if a user or guild is blacklisted (Developer only)')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('user')
                    .setDescription('Check if a user is blacklisted')
                    .addUserOption(option =>
                        option.setName('target')
                            .setDescription('The user to check')
                            .setRequired(true)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('guild')
                    .setDescription('Check if a guild is blacklisted')
                    .addStringOption(option =>
                        option.setName('guild_id')
                            .setDescription('The guild ID to check (leave empty for current guild)')
                            .setRequired(false))),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const subcommand = interaction.options.getSubcommand();

    try {
        if (subcommand === 'user') {
            const targetUser = interaction.options.getUser('target');
            
            // Create fake interaction for blacklist check
            const fakeInteraction = {
                user: targetUser,
                guild: interaction.guild
            };

            // Check local blacklist
            const localBlacklistCheck = await checkLocalBlacklist(fakeInteraction);

            const embed = new EmbedBuilder()
                .setTitle('🔍 Blacklist Check - User')
                .setColor(localBlacklistCheck.isBlacklisted ? 0xFF0000 : 0x00FF00)
                .addFields(
                    { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: false },
                    { name: 'Local Blacklist', value: localBlacklistCheck.isBlacklisted ? `❌ **Blacklisted**\nReason: ${localBlacklistCheck.reason || 'No reason provided'}` : '✅ Not blacklisted', inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed], ephemeral: true });

        } else if (subcommand === 'guild') {
            const guildId = interaction.options.getString('guild_id') || interaction.guild?.id;
            
            if (!guildId) {
                return interaction.reply({
                    content: '❌ Please provide a guild ID or use this command in a server.',
                    ephemeral: true
                });
            }

            // Check local blacklist
            const LocalBlacklist = require('../../../models/LocalBlacklist');
            const localBlacklist = await LocalBlacklist.findOne({ 
                userId: guildId,
                guildId: guildId,
                type: 'guild'
            });

            const guildName = interaction.client.guilds.cache.get(guildId)?.name || 'Unknown Guild';

            const embed = new EmbedBuilder()
                .setTitle('🔍 Blacklist Check - Guild')
                .setColor(localBlacklist ? 0xFF0000 : 0x00FF00)
                .addFields(
                    { name: 'Guild', value: `${guildName} (${guildId})`, inline: false },
                    { name: 'Local Blacklist', value: localBlacklist ? `❌ **Blacklisted**\nReason: ${localBlacklist.reason}` : '✅ Not blacklisted', inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed], ephemeral: true });
        }

    } catch (error) {
        console.error('Error checking blacklist:', error);
        await interaction.reply({
            content: '❌ An error occurred while checking the blacklist.',
            ephemeral: true
        });
    }
};
