const { SlashCommandBuilder } = require('discord.js');
const Settings = require('../../../schemas/settings');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ["ManageGuild"],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('settings')
            .setDescription('Manage server settings')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('setlogchannel')
                    .setDescription('Set the logging channel for restrictions')
                    .addChannelOption(option =>
                        option.setName('channel')
                            .setDescription('The channel to set as the restrictions logging channel')
                            .setRequired(true)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('setappeallogchannel')
                    .setDescription('Set the logging channel for appeals')
                    .addChannelOption(option =>
                        option.setName('channel')
                            .setDescription('The channel to set as the appeals logging channel')
                            .setRequired(true)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('setrestrictionrole')
                    .setDescription('Set the restriction role ID')
                    .addRoleOption(option =>
                        option.setName('role')
                            .setDescription('The role to set as the restriction role')
                            .setRequired(true))),
    },
};

module.exports.slashRun = async (interaction, client) => {
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (ownerIds.includes(interaction.user.id)) {
        // Bypass all permission checks for owners
    } else {
        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'setlogchannel' || subcommand === 'setappeallogchannel' || subcommand === 'setrestrictionrole') {
            const settings = await Settings.findOne({ guildId: interaction.guild.id });
            const staffRoleId = settings?.staffRoleId;

            if (!staffRoleId) {
                return interaction.reply({
                    content: '❌ The staff role is not set up. Please use `/setstaffrole` to configure it.',
                    ephemeral: true,
                });
            }

            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return interaction.reply({
                    content: '❌ You do not have the required staff role to use this command.',
                    ephemeral: true,
                });
            }
        }
    }

    const subcommand = interaction.options.getSubcommand();

    if (subcommand === 'setlogchannel') {
        const channel = interaction.options.getChannel('channel');

        if (!channel || !channel.isTextBased()) {
            return interaction.reply({
                content: 'Please select a valid text channel.',
                ephemeral: true,
            });
        }

        await Settings.findOneAndUpdate(
            { guildId: interaction.guild.id },
            { logChannelId: channel.id },
            { upsert: true }
        );

        interaction.reply({
            content: `Restrictions logging channel has been set to ${channel.name}.`
        });
    } else if (subcommand === 'setappeallogchannel') {
        const channel = interaction.options.getChannel('channel');

        if (!channel || !channel.isTextBased()) {
            return interaction.reply({
                content: 'Please select a valid text channel.',
                ephemeral: true,
            });
        }

        await Settings.findOneAndUpdate(
            { guildId: interaction.guild.id },
            { appealLogChannelId: channel.id },
            { upsert: true }
        );

        interaction.reply({
            content: `Appeals logging channel has been set to ${channel.name}.`
        });
    } else if (subcommand === 'setrestrictionrole') {
        const role = interaction.options.getRole('role');

        if (!role) {
            return interaction.reply({
                content: 'Please select a valid role.',
                ephemeral: true,
            }); 
        }

        await Settings.findOneAndUpdate(
            { guildId: interaction.guild.id },
            { restrictionRoleId: role.id },
            { upsert: true }
        );

        interaction.reply({
            content: `Restriction role has been set to ${role.name}.`
        });
    }
};
