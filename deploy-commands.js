const { REST, Routes } = require('discord.js');
const fs = require('fs');
require('dotenv').config();

const commands = [];
const commandFolders = [
  './commands/Slash/Utility',
  './commands/Slash/Owner',
  './commands/Slash/Settings',
  './commands/Slash/Users'
];

for (const folder of commandFolders) {
  if (!fs.existsSync(folder)) continue;
  const commandFiles = fs.readdirSync(folder).filter(file => file.endsWith('.js'));
  for (const file of commandFiles) {
    const command = require(`${folder}/${file}`);
    if (command.help && command.help.Slash && command.help.Slash.data) {
      commands.push(command.help.Slash.data.toJSON());
    }
  }
}

const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);

(async () => {
  try {
    console.log('Started refreshing application (/) commands.');
    await rest.put(
      Routes.applicationCommands(process.env.APP_ID),
      { body: commands },
    );
    console.log('Successfully reloaded application (/) commands.');
  } catch (error) {
    console.error(error);
  }
})();
