# 📁 Blacklist System - Complete Code Files

This document contains all the code files needed to implement the advanced blacklist system. Copy each file to the specified location in your bot project.

---

## 📄 File 1: `models/LocalBlacklist.js`

```javascript
const mongoose = require('mongoose');

const localBlacklistSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    guildId: { type: String, required: true },
    reason: { type: String, required: true },
    blacklistedBy: { type: String, required: true },
    blacklistedAt: { type: Date, default: Date.now },
    type: { type: String, enum: ['user', 'guild'], required: true }
});

// Create compound index for efficient lookups
localBlacklistSchema.index({ userId: 1, guildId: 1 }, { unique: true });

module.exports = mongoose.model('LocalBlacklist', localBlacklistSchema);
```

---

## 📄 File 2: `middleware/localBlacklist.js`

```javascript
const LocalBlacklist = require('../models/LocalBlacklist');
const { sendBlacklistLog } = require('../utils/blacklistLogger');

// Get developer IDs from environment variable
function getDeveloperIds() {
    const devIds = process.env.DEVELOPER_IDS;
    if (!devIds) return [];
    return devIds.split(',').map(id => id.trim());
}

function isDeveloper(userId) {
    const developers = getDeveloperIds();
    return developers.includes(userId);
}

async function checkLocalBlacklist(interaction) {
    try {
        // Skip blacklist check for developers
        if (isDeveloper(interaction.user.id)) {
            return { isBlacklisted: false };
        }

        console.log(`🔍 Checking local blacklist for user: ${interaction.user.tag} (${interaction.user.id})`);

        // Check if user is locally blacklisted in this guild
        let userBlacklist = null;
        let guildCheck = 'NOT FOUND';
        let globalCheck = 'NOT FOUND';
        
        if (interaction.guild) {
            userBlacklist = await LocalBlacklist.findOne({ 
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                type: 'user'
            });
            guildCheck = userBlacklist ? 'FOUND' : 'NOT FOUND';
            console.log(`🔍 Guild-specific blacklist check (${interaction.guild.id}):`, guildCheck);
        }

        // If not found in guild-specific, check global blacklist
        if (!userBlacklist) {
            userBlacklist = await LocalBlacklist.findOne({ 
                userId: interaction.user.id,
                guildId: 'global',
                type: 'user'
            });
            globalCheck = userBlacklist ? 'FOUND' : 'NOT FOUND';
            console.log(`🔍 Global blacklist check:`, globalCheck);
        }

        // Log the check
        sendBlacklistLog('USER_CHECK', {
            username: interaction.user.tag,
            userId: interaction.user.id,
            guildName: interaction.guild?.name,
            guildId: interaction.guild?.id,
            guildCheck,
            globalCheck
        });

        if (userBlacklist) {
            console.log(`🚫 User ${interaction.user.tag} is blacklisted: ${userBlacklist.reason}`);
            
            // Log user blocked
            sendBlacklistLog('USER_BLOCKED', {
                username: interaction.user.tag,
                userId: interaction.user.id,
                command: interaction.commandName || 'Unknown',
                guildName: interaction.guild?.name,
                guildId: interaction.guild?.id,
                reason: userBlacklist.reason
            });
            
            const supportServer = process.env.SUPPORT_SERVER_URL || 'https://discord.gg/your-support-server';
            return {
                isBlacklisted: true,
                type: 'user',
                reason: userBlacklist.reason,
                message: `❌ **You are blacklisted from using this bot.**\n\n**Reason:** ${userBlacklist.reason}\n**Blacklisted by:** <@${userBlacklist.blacklistedBy}>\n\n🔗 **Need help?** Join our support server: ${supportServer}`
            };
        }

        // Check if guild is locally blacklisted (only in guild context)
        if (interaction.guild) {
            const guildBlacklist = await LocalBlacklist.findOne({ 
                userId: interaction.guild.id,
                guildId: interaction.guild.id,
                type: 'guild'
            });
            console.log(`🔍 Guild blacklist check (${interaction.guild.id}):`, guildBlacklist ? 'BLACKLISTED' : 'NOT BLACKLISTED');

            if (guildBlacklist) {
                console.log(`🚫 Guild ${interaction.guild.name} is blacklisted: ${guildBlacklist.reason}`);
                const supportServer = process.env.SUPPORT_SERVER_URL || 'https://discord.gg/your-support-server';
                return {
                    isBlacklisted: true,
                    type: 'guild',
                    reason: guildBlacklist.reason,
                    message: `❌ **This server is blacklisted from using this bot.**\n\n**Reason:** ${guildBlacklist.reason}\n**Blacklisted by:** <@${guildBlacklist.blacklistedBy}>\n\n🔗 **Need help?** Join our support server: ${supportServer}`
                };
            }
        }

        console.log(`✅ User ${interaction.user.tag} is not blacklisted`);
        return { isBlacklisted: false };

    } catch (error) {
        console.error('Error checking local blacklist:', error);
        return { isBlacklisted: false }; // Allow command to proceed if check fails
    }
}

async function addToLocalBlacklist(targetId, guildId, reason, blacklistedBy, type = 'user') {
    try {
        const blacklistEntry = new LocalBlacklist({
            userId: targetId,
            guildId: guildId || 'global',
            reason,
            blacklistedBy,
            type
        });

        await blacklistEntry.save();
        return { success: true };
    } catch (error) {
        if (error.code === 11000) {
            return { success: false, error: 'User/Guild is already blacklisted' };
        }
        console.error('Error adding to local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

async function removeFromLocalBlacklist(targetId, guildId, type = 'user') {
    try {
        const result = await LocalBlacklist.deleteOne({
            userId: targetId,
            guildId: guildId || 'global',
            type
        });

        return { success: result.deletedCount > 0 };
    } catch (error) {
        console.error('Error removing from local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

async function getLocalBlacklist(guildId, type = null) {
    try {
        const query = { guildId: guildId || 'global' };
        if (type) query.type = type;

        const blacklist = await LocalBlacklist.find(query).sort({ blacklistedAt: -1 });
        return { success: true, blacklist };
    } catch (error) {
        console.error('Error getting local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

module.exports = {
    checkLocalBlacklist,
    addToLocalBlacklist,
    removeFromLocalBlacklist,
    getLocalBlacklist,
    isDeveloper,
    getDeveloperIds
};
```

---

## 📄 File 3: `utils/blacklistLogger.js`

```javascript
const { WebhookClient, EmbedBuilder } = require('discord.js');

let blacklistWebhook = null;

// Initialize webhook client
function initializeBlacklistLogger() {
    const webhookUrl = process.env.BLACKLIST_WEBHOOK_URL;
    if (webhookUrl && webhookUrl !== 'your_blacklist_webhook_url_here') {
        try {
            blacklistWebhook = new WebhookClient({ url: webhookUrl });
            console.log('✅ Blacklist webhook logger initialized');
        } catch (error) {
            console.error('❌ Failed to initialize blacklist webhook:', error);
        }
    } else {
        console.log('⚠️ Blacklist webhook URL not configured');
    }
}

// Send log to webhook
async function sendBlacklistLog(type, data) {
    if (!blacklistWebhook) return;

    try {
        const embed = new EmbedBuilder()
            .setTimestamp()
            .setFooter({ text: 'Bot Blacklist System' });

        switch (type) {
            case 'USER_CHECK':
                embed
                    .setTitle('🔍 User Blacklist Check')
                    .setColor(0x3498DB)
                    .addFields(
                        { name: 'User', value: `${data.username} (${data.userId})`, inline: true },
                        { name: 'Guild', value: data.guildName ? `${data.guildName} (${data.guildId})` : 'DM', inline: true },
                        { name: 'Guild Check', value: data.guildCheck, inline: true },
                        { name: 'Global Check', value: data.globalCheck, inline: true }
                    );
                break;

            case 'USER_BLOCKED':
                embed
                    .setTitle('🚫 User Blocked')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'User', value: `${data.username} (${data.userId})`, inline: true },
                        { name: 'Command', value: data.command, inline: true },
                        { name: 'Guild', value: data.guildName ? `${data.guildName} (${data.guildId})` : 'DM', inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'GUILD_JOIN':
                embed
                    .setTitle('🏠 Guild Joined')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Member Count', value: data.memberCount?.toString() || 'Unknown', inline: true },
                        { name: 'Owner', value: data.ownerId ? `<@${data.ownerId}>` : 'Unknown', inline: true }
                    );
                break;

            case 'GUILD_BLACKLISTED_LEAVE':
                embed
                    .setTitle('🚫 Left Blacklisted Guild')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Reason', value: data.reason || 'Guild is blacklisted', inline: true },
                        { name: 'Action', value: 'Auto-left guild', inline: true }
                    );
                break;

            case 'GUILD_FORCE_LEAVE':
                embed
                    .setTitle('🚪 Force Left Guild')
                    .setColor(0x9B59B6)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Executed By', value: `${data.executedBy} (${data.executedById})`, inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'USER_BLACKLISTED':
                embed
                    .setTitle('🚫 User Added to Blacklist')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Target User', value: `${data.targetUser} (${data.targetUserId})`, inline: true },
                        { name: 'Blacklisted By', value: `${data.blacklistedBy} (${data.blacklistedById})`, inline: true },
                        { name: 'Scope', value: data.scope, inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'USER_UNBLACKLISTED':
                embed
                    .setTitle('✅ User Removed from Blacklist')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Target User', value: `${data.targetUser} (${data.targetUserId})`, inline: true },
                        { name: 'Removed By', value: `${data.removedBy} (${data.removedById})`, inline: true },
                        { name: 'Scope', value: data.scope, inline: true }
                    );
                break;

            case 'GUILD_BLACKLISTED':
                embed
                    .setTitle('🚫 Guild Added to Blacklist')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Target Guild', value: `${data.guildName || 'Unknown'} (${data.guildId})`, inline: true },
                        { name: 'Blacklisted By', value: `${data.blacklistedBy} (${data.blacklistedById})`, inline: true },
                        { name: 'Auto-Left', value: data.autoLeft ? 'Yes' : 'No', inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'GUILD_UNBLACKLISTED':
                embed
                    .setTitle('✅ Guild Removed from Blacklist')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Target Guild', value: `${data.guildName || 'Unknown'} (${data.guildId})`, inline: true },
                        { name: 'Removed By', value: `${data.removedBy} (${data.removedById})`, inline: true }
                    );
                break;

            default:
                embed
                    .setTitle('📝 Blacklist Log')
                    .setColor(0x95A5A6)
                    .setDescription(`Unknown log type: ${type}`)
                    .addFields({ name: 'Data', value: JSON.stringify(data, null, 2).substring(0, 1000), inline: false });
        }

        await blacklistWebhook.send({ embeds: [embed] });
    } catch (error) {
        console.error('❌ Failed to send blacklist log to webhook:', error);
    }
}

module.exports = {
    initializeBlacklistLogger,
    sendBlacklistLog
};
```

---

## 📄 File 4: `commands/test-blacklist.js`

```javascript
const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('test-blacklist')
        .setDescription('Test command to check if blacklist is working'),
    async execute(interaction) {
        await interaction.reply({
            content: '✅ **Test successful!** You are not blacklisted and can use commands.',
            flags: 64 // MessageFlags.Ephemeral
        });
    },
};
```

---

## 📄 File 5: `commands/blacklist/blacklist-user.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { addToLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('blacklist-user')
        .setDescription('Blacklist a user from using the bot (Developer only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to blacklist')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for blacklisting')
                .setRequired(true))
        .addBooleanOption(option =>
            option.setName('global')
                .setDescription('Apply blacklist globally (all servers)')
                .setRequired(false)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const targetUser = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason');
        const isGlobal = interaction.options.getBoolean('global') || false;
        const guildId = isGlobal ? 'global' : interaction.guild.id;

        // Prevent blacklisting other developers
        if (isDeveloper(targetUser.id)) {
            return interaction.reply({
                content: '❌ Cannot blacklist other developers.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        // Prevent self-blacklisting
        if (targetUser.id === interaction.user.id) {
            return interaction.reply({
                content: '❌ You cannot blacklist yourself.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        try {
            const result = await addToLocalBlacklist(
                targetUser.id,
                guildId,
                reason,
                interaction.user.id,
                'user'
            );

            if (result.success) {
                // Log the blacklist action
                sendBlacklistLog('USER_BLACKLISTED', {
                    targetUser: targetUser.tag,
                    targetUserId: targetUser.id,
                    blacklistedBy: interaction.user.tag,
                    blacklistedById: interaction.user.id,
                    scope: isGlobal ? 'Global' : 'Server-specific',
                    reason: reason
                });

                const embed = new EmbedBuilder()
                    .setTitle('✅ User Blacklisted')
                    .setColor(0xFF0000)
                    .addFields(
                        { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: true },
                        { name: 'Reason', value: reason, inline: true },
                        { name: 'Scope', value: isGlobal ? 'Global' : 'This Server', inline: true },
                        { name: 'Blacklisted by', value: `${interaction.user}`, inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [embed] });
            } else {
                await interaction.reply({
                    content: `❌ Failed to blacklist user: ${result.error}`,
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } catch (error) {
            console.error('Error blacklisting user:', error);
            await interaction.reply({
                content: '❌ An error occurred while blacklisting the user.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```
