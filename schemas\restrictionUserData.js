const mongoose = require('mongoose');

const restrictionUserDataSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    serverId: { type: String, required: true }, // Ensure this is required
    serverName: { type: String, required: true }, // Ensure this is required
    restrictionReason: { type: String, default: 'No reason provided' },
    isAppealable: { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },
    roles: { type: [String], default: [] },
    uniqueCode: { type: String, required: true },
    createdAt: { type: Date, default: Date.now },
    // Auto-ban tracking fields
    leftDuringRestriction: { type: Boolean, default: false },
    autoBanned: { type: Boolean, default: false },
    autoBanReason: { type: String, default: null },
    autoBanDate: { type: Date, default: null },
    // GDPR protection field
    gdprProtected: { type: Boolean, default: false },
});

module.exports = mongoose.model('RestrictionUserData', restrictionUserDataSchema);