# 🤖 Star-Gate Bot - Complete Functionality Documentation

## 📋 **Overview**
Star-Gate is a comprehensive Discord moderation and utility bot designed for server management, user restrictions, blacklist enforcement, and administrative tools.

---

## 🔐 **Required Permissions**

### **Essential Bot Permissions:**
- **Send Messages** - Post responses and notifications
- **Embed Links** - Display rich embed messages
- **Read Message History** - Access channel content for moderation
- **Manage Messages** - Delete messages for moderation
- **Manage Channels** - Update voice channel names for statistics
- **Manage Roles** - Add/remove restriction roles
- **Ban Members** - Auto-ban users who evade restrictions
- **Kick Members** - Remove problematic users
- **View Channels** - Access server channels
- **Connect** - Access voice channels for statistics
- **Use Slash Commands** - Execute bot commands

### **Administrative Permissions (for full functionality):**
- **Administrator** - Required for setup commands and sensitive operations
- **Manage Guild** - Server configuration access
- **Manage Webhooks** - Logging system integration

---

## 📊 **Data Collection & Storage**

### **User Data Collected:**
- **Discord User ID** - Unique identifier for users
- **Username/Tag** - Display names for logging
- **Guild Membership** - Server participation tracking
- **Role Assignments** - Restriction role management
- **Command Usage** - Cooldown and usage tracking
- **Restriction History** - Moderation records and appeals
- **Appeal Submissions** - User-submitted restriction appeals

### **Server Data Collected:**
- **Guild ID** - Unique server identifier
- **Guild Name** - Server name for logging
- **Channel IDs** - Configured logging and restriction channels
- **Role IDs** - Staff and restriction role configurations
- **Member Count** - Server statistics
- **Channel Permissions** - Access verification

### **Database Storage:**
- **MongoDB Database** - Persistent data storage
- **Restriction Records** - User restriction history and status
- **Server Settings** - Guild-specific configurations
- **Blacklist Entries** - User and guild blacklist records
- **User Profiles** - Premium status and flags
- **Appeal Data** - Restriction appeal submissions and responses

---

## 🎮 **Command Categories & Functions**

### **🛡️ Moderation Commands**

#### **`/restriction`** (Administrator)
- **Purpose**: Manage user restrictions
- **Subcommands**: `add`, `remove`, `view`
- **Data Access**: User profiles, roles, restriction history
- **Actions**: Add/remove restriction roles, update database records
- **Logging**: Creates restriction records, logs to channels

#### **`/settings`** (Administrator)
- **Purpose**: Configure server settings
- **Data Access**: Server configuration, channel/role IDs
- **Actions**: Set logging channels, restriction roles, staff roles
- **Storage**: Saves guild settings to database

#### **`/clear`** (Manage Messages)
- **Purpose**: Bulk delete messages
- **Data Access**: Channel message history
- **Actions**: Delete specified number of messages
- **Logging**: Records deletion activity

### **🚫 Blacklist System (Developer Only)**

#### **`/blacklist-user`** (Developer)
- **Purpose**: Blacklist users from bot usage
- **Data Access**: User IDs, guild information
- **Actions**: Add users to blacklist database
- **Scope**: Server-specific or global blacklists

#### **`/blacklist-guild`** (Developer)
- **Purpose**: Blacklist entire servers
- **Data Access**: Guild IDs, member lists
- **Actions**: Add guilds to blacklist, auto-leave servers
- **Enforcement**: Automatic departure from blacklisted servers

#### **`/blacklist-check`** (Developer)
- **Purpose**: Verify blacklist status
- **Data Access**: Blacklist database records
- **Actions**: Query and display blacklist information

#### **`/force-leave`** (Developer)
- **Purpose**: Force bot departure from servers
- **Data Access**: Guild information
- **Actions**: Leave specified servers with logging

### **📊 Statistics & Monitoring**

#### **`/productionstats`** (Administrator)
- **Purpose**: Display server restriction statistics
- **Data Access**: Restriction database, role members, server info
- **Information Shown**: Active restrictions, appeal counts, auto-bans
- **Real-time**: Current role member counts

#### **`/setupproductionchannel`** (Administrator)
- **Purpose**: Configure automatic voice channel updates
- **Data Access**: Voice channels, restriction role members
- **Actions**: Set channel names to show restriction counts
- **Updates**: Real-time count updates when restrictions change

### **👤 User Commands**

#### **`/info`** (Public)
- **Purpose**: Display user information
- **Data Access**: User profiles, join dates, roles
- **Information**: Account creation, server join date, role list

#### **`/avatar`** (Public)
- **Purpose**: Display user avatars
- **Data Access**: User profile pictures
- **Actions**: Fetch and display avatar images

#### **`/ping`** (Public)
- **Purpose**: Check bot responsiveness
- **Data Access**: Bot latency, API response times
- **Information**: Connection status and performance

### **📢 Administrative Tools**

#### **`/announcement`** (Owner)
- **Purpose**: Send announcements to all servers
- **Data Access**: All guild channels, logging configurations
- **Actions**: Post messages to logging or public channels
- **Reach**: All servers where bot has permissions

#### **`/owner`** (Owner)
- **Purpose**: Owner-specific administrative functions
- **Data Access**: Full bot and server access
- **Actions**: High-level bot management

---

## 🔄 **Automatic Functions**

### **Real-Time Monitoring:**
- **Restriction Role Tracking** - Monitors role assignments
- **Voice Channel Updates** - Updates channel names with counts
- **Bot Status Updates** - Shows global restriction statistics
- **Blacklist Enforcement** - Blocks blacklisted users/guilds

### **Event Handlers:**
- **Guild Join** - Auto-leave blacklisted servers
- **Member Leave** - Auto-ban restricted users who leave
- **Interaction Create** - Process all commands and interactions
- **Channel Create** - Apply permission restrictions to new channels

### **Automated Enforcement:**
- **Restriction Evasion Prevention** - Ban users who leave while restricted
- **Blacklist Compliance** - Block access for blacklisted entities
- **Permission Management** - Automatic role and permission handling

---

## 📝 **Logging & Webhooks**

### **Webhook Integrations:**
- **Blacklist Events** - User/guild blacklist actions
- **Restriction Changes** - Addition/removal of restrictions
- **Guild Activities** - Server joins/leaves, auto-bans
- **System Events** - Bot startup, errors, administrative actions

### **Channel Logging:**
- **Restriction Logs** - User restriction activities
- **Appeal Logs** - Restriction appeal submissions and responses
- **Auto-Ban Logs** - Automatic ban enforcement
- **Administrative Logs** - Settings changes and admin actions

---

## 🛡️ **Security Features**

### **Access Control:**
- **Developer-Only Commands** - Blacklist management restricted to developers
- **Administrator Requirements** - Sensitive commands require admin permissions
- **Owner Verification** - Announcement commands limited to bot owners
- **Permission Validation** - All actions verify appropriate permissions

### **Data Protection:**
- **Secure Database Storage** - MongoDB with proper authentication
- **Permission Verification** - Commands check user permissions before execution
- **Error Handling** - Graceful failure without data exposure
- **Audit Trails** - Complete logging of all administrative actions

### **Anti-Evasion Measures:**
- **Auto-Ban System** - Prevents restriction evasion by leaving
- **Blacklist Enforcement** - Blocks access for problematic users/servers
- **Role Persistence** - Maintains restrictions across sessions
- **Real-Time Monitoring** - Immediate response to evasion attempts

---

## 🔧 **Technical Requirements**

### **Bot Dependencies:**
- **Discord.js v14** - Discord API interaction
- **MongoDB** - Database storage and management
- **Node.js Runtime** - JavaScript execution environment
- **Webhook URLs** - External logging integration

### **Environment Variables:**
- **Bot Token** - Discord bot authentication
- **Database URI** - MongoDB connection string
- **Developer IDs** - Authorized developer user IDs
- **Webhook URLs** - Logging and notification endpoints
- **Owner IDs** - Bot owner identification

---

## 📊 **Data Retention**

### **Permanent Storage:**
- **Restriction Records** - Historical moderation data
- **Server Settings** - Guild configuration preferences
- **Blacklist Entries** - Security and enforcement records
- **Appeal History** - User appeal submissions and responses

### **Temporary Storage:**
- **Command Cooldowns** - In-memory cooldown tracking
- **Cache Data** - Discord API response caching
- **Session Data** - Temporary interaction state

---

## 🎯 **Bot Purpose & Use Cases**

### **Primary Functions:**
1. **Server Moderation** - User restriction and role management
2. **Security Enforcement** - Blacklist and anti-evasion systems
3. **Administrative Tools** - Server configuration and management
4. **Statistics Tracking** - Real-time moderation analytics
5. **Communication** - Announcement and notification systems

### **Target Users:**
- **Server Administrators** - Primary bot users for moderation
- **Moderators** - Staff members with restricted access
- **Bot Developers** - Full system access for maintenance
- **Server Members** - Limited access to utility commands

This documentation provides complete transparency about the bot's functionality, data usage, and permission requirements for Terms of Service and privacy policy creation.

---

## 📋 **Additional Utility Commands**

### **Information & Help Commands:**
- **`/help`** - Display available commands and usage
- **`/about`** - Bot information and statistics
- **`/commands`** - List all available commands
- **`/howto`** - Usage guides and tutorials
- **`/updates`** - View bot changelog and recent updates
- **`/changelog`** - Detailed version history
- **`/legal`** - Terms of service and legal information
- **`/credits`** - Development team and acknowledgments

### **Server Utility Commands:**
- **`/invite`** - Generate bot invite links
- **`/stats`** - Server and bot statistics
- **`/viewsettings`** - Display current server configuration
- **`/viewHistory`** - View user restriction history
- **`/restrictedHistory`** - View restriction records

### **User Interaction Commands:**
- **`/suggestion`** - Submit suggestions for bot improvements
- **`/bug`** - Report bugs and issues
- **`/test-blacklist`** - Test blacklist system functionality

---

## 🔍 **Data Processing Details**

### **Message Content Access:**
- **Command Processing** - Reads slash command parameters
- **Moderation Actions** - Accesses message content for deletion
- **User Mentions** - Processes user and role mentions
- **Channel References** - Reads channel IDs and names

### **Member Data Processing:**
- **Role Management** - Reads and modifies user roles
- **Permission Checking** - Verifies user permissions
- **Presence Information** - Basic online/offline status
- **Join/Leave Tracking** - Member activity monitoring

### **Guild Information Access:**
- **Channel Lists** - Reads all server channels
- **Role Hierarchies** - Accesses server role structure
- **Member Lists** - Reads server member information
- **Server Settings** - Guild configuration data

---

## ⚠️ **Important Privacy Notes**

### **Data Minimization:**
- Only collects data necessary for functionality
- No personal information beyond Discord-provided data
- No message content storage (except for moderation logs)
- Automatic cleanup of expired temporary data

### **User Rights:**
- Users can request data deletion (contact developers)
- Restriction appeals provide data review process
- Blacklist removal available through proper channels
- Settings can be reset by server administrators

### **Third-Party Integrations:**
- **Discord API** - All bot functionality relies on Discord's API
- **MongoDB Atlas** - Database hosting and management
- **Webhook Services** - External logging and notifications
- **No other third-party data sharing**

---

## 🚨 **Emergency Procedures**

### **Data Breach Response:**
- Immediate database access restriction
- User notification through Discord channels
- Developer team emergency protocols
- Audit trail preservation

### **Bot Malfunction:**
- Automatic error logging and reporting
- Graceful degradation of non-essential features
- Emergency shutdown procedures
- Data integrity protection measures

### **Abuse Prevention:**
- Rate limiting on all commands
- Permission verification for sensitive actions
- Automatic blacklist enforcement
- Developer oversight and monitoring

This comprehensive documentation ensures full transparency for Terms of Service, Privacy Policy, and user agreement creation.
