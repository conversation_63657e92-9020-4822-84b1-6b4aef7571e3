const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { WebhookClient } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

// Initialize webhook
const BUG_WEBHOOK_URL = process.env.BUG_WEBHOOK;
const bugWebhook = BUG_WEBHOOK_URL ? new WebhookClient({ url: BUG_WEBHOOK_URL }) : null;

module.exports = {
  data: new SlashCommandBuilder()
    .setName('bug')
    .setDescription('Report a bug to the bot owner')
    .addStringOption(opt => opt
      .setName('summary')
      .setDescription('Brief summary of the bug (max 1024 chars)')
      .setRequired(true)
    )
    .addStringOption(opt => opt
      .setName('steps')
      .setDescription('Steps to reproduce the bug (max 1024 chars)')
      .setRequired(true)
    )
    .addStringOption(opt => opt
      .setName('severity')
      .setDescription('Severity level')
      .setRequired(true)
      .addChoices(
        { name: 'Low', value: 'Low' },
        { name: 'Medium', value: 'Medium' },
        { name: 'High', value: 'High' },
        { name: 'Critical', value: 'Critical' }
      )
    )
    .addAttachmentOption(opt => opt
      .setName('screenshot')
      .setDescription('Optional screenshot')
    ),

  async execute(interaction) {
    await interaction.deferReply({ ephemeral: true });
    if (!bugWebhook) {
      return interaction.editReply('⚠️ Bug-report system not configured.');
    }

    // Gather inputs
    const summary = interaction.options.getString('summary').slice(0, 1024);
    const steps = interaction.options.getString('steps').slice(0, 1024);
    const severity = interaction.options.getString('severity');
    const screenshot = interaction.options.getAttachment('screenshot');

    // Build embed
    const embed = new EmbedBuilder()
      .setTitle('🐞 New Bug Report')
      .setDescription(summary)
      .addFields(
        { name: 'Steps to Reproduce', value: steps },
        { name: 'Severity', value: severity, inline: true }
      )
      .setColor(0xFF0000)
      .setFooter({ text: `${(interaction.guild?.name || 'DMs').slice(0, 1024)} (${interaction.guildId})` })
      .setTimestamp();

    // Attach screenshot if present
    if (screenshot && screenshot.url?.startsWith('http')) {
      embed.setImage(screenshot.url);
    }

    // Send to webhook
    try {
      await bugWebhook.send({ embeds: [embed] });
      await interaction.editReply('✅ Bug report submitted. Thank you!');
    } catch (err) {
      console.error('Webhook error:', err);
      await interaction.editReply('❌ Failed to submit bug report.');
    }
  }
};
