const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { removeFromLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('unblacklist-user')
            .setDescription('Remove a user from the blacklist (Developer only)')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to unblacklist')
                    .setRequired(true))
            .addBooleanOption(option =>
                option.setName('global')
                    .setDescription('Remove from global blacklist')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const targetUser = interaction.options.getUser('user');
    const isGlobal = interaction.options.getBoolean('global') || false;
    const guildId = isGlobal ? 'global' : interaction.guild.id;

    try {
        const result = await removeFromLocalBlacklist(targetUser.id, guildId, 'user');

        if (result.success) {
            // Log the unblacklist action
            sendBlacklistLog('USER_UNBLACKLISTED', {
                targetUser: targetUser.tag,
                targetUserId: targetUser.id,
                removedBy: interaction.user.tag,
                removedById: interaction.user.id,
                scope: isGlobal ? 'Global' : 'Server-specific'
            });

            const embed = new EmbedBuilder()
                .setTitle('✅ User Unblacklisted')
                .setColor(0x00FF00)
                .addFields(
                    { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: true },
                    { name: 'Scope', value: isGlobal ? 'Global' : 'This Server', inline: true },
                    { name: 'Unblacklisted by', value: `${interaction.user}`, inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed] });
        } else {
            await interaction.reply({
                content: '❌ User was not found in the blacklist.',
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error unblacklisting user:', error);
        await interaction.reply({
            content: '❌ An error occurred while unblacklisting the user.',
            ephemeral: true
        });
    }
};
