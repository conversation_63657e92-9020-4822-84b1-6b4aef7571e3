[{"date": "2024-06-09", "title": "Initial Release", "description": "Star-Gate bot launched with core moderation and utility features."}, {"date": "2024-06-09", "title": "How-To Command Added", "description": "Added /howto command to help users get started and understand bot features."}, {"date": "2024-06-09", "title": "Restricted VC Counter", "description": "Added /setuprestrictedvc to show restricted user count in a voice channel."}, {"date": "2025-01-15", "title": "Production Statistics System", "description": "Added comprehensive production statistics with /productionstats command, automatic voice channel updates, and real-time bot status showing restricted user counts."}, {"date": "2025-01-15", "title": "Advanced Blacklist System", "description": "Implemented enterprise-level blacklist system with 8 developer commands, webhook logging, auto-leave for blacklisted guilds, and comprehensive security features."}, {"date": "2025-01-15", "title": "Enhanced Production Channel Setup", "description": "Added /setupproductionchannel command with better error handling, production stats integration, and detailed setup information."}, {"date": "2025-01-15", "title": "Real-Time Voice Channel Updates", "description": "Voice channels now automatically update restricted user counts in real-time when restrictions are added/removed, with 30-second backup sync."}, {"date": "2025-01-15", "title": "Bot Status Enhancement", "description": "Bot status now shows total restricted users from database (all servers combined) while respecting maintenance and developer modes."}, {"date": "2025-01-15", "title": "Critical Database Error Fix", "description": "Fixed ReferenceError: db is not defined crash by implementing reliable in-memory Map-based cooldown system with automatic cleanup."}, {"date": "2025-01-15", "title": "Production Statistics Accuracy Fix", "description": "Fixed inaccurate restricted user counting - bot status shows global database count, voice channels show server-specific role member count only."}, {"date": "2025-01-15", "title": "RestrictionUserData Validation Fix", "description": "Fixed validation errors for missing serverId and serverName fields in existing restriction records with enhanced migration support."}, {"date": "2025-01-15", "title": "Code Cleanup and Optimization", "description": "Removed duplicate setuprestrictedvc.js command, keeping enhanced setupproductionchannel.js. Improved error handling and performance."}, {"date": "2025-01-15", "title": "Webhook Integration", "description": "Added comprehensive webhook logging for all blacklist events, restriction changes, and system activities with rich embeds and real-time notifications."}, {"date": "2025-01-15", "title": "Auto-Ban for Restriction Evasion", "description": "Users who leave the server while restricted are now automatically banned with reason 'Left during restriction - uncooperative with investigation' to prevent evasion. Includes logging and statistics tracking."}, {"date": "2025-01-15", "title": "GDPR Blacklist System", "description": "Added /blacklist-gdpr, /unblacklist-gdpr, and /gdpr-logs commands for GDPR data deletion compliance. Includes structured database logging with user_id, reason, flags, timestamp, and issuer tracking. GDPR blacklisted users cannot use any commands or be restricted."}, {"date": "2025-01-15", "title": "GDPR Webhook Logging for Legal Compliance", "description": "Added dedicated GDPR webhook logging system for legal audit trails. Logs all GDPR blacklist applications, access attempts, compliance checks, and database operations with comprehensive legal documentation and audit information."}, {"date": "2025-01-15", "title": "Enhanced GDPR Data Protection", "description": "GDPR users can now be restricted but with minimal data logging (user ID only). History commands show 'protected under GDPR' message. Restrictions only log to GDPR webhook, not regular channels or history commands."}, {"date": "2025-01-15", "title": "GDPR Admin Panel for Owners", "description": "Added /gdpr-admin command for owners to monitor all GDPR blacklisted users, restricted users, user details, and system statistics. GDPR restrictions now store real server names and reasons for administrative tracking."}]