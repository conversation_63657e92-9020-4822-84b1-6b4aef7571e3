const { Client, Partials, GatewayIntentBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const logger = require('./utils/logging/logManager.js'); 
require('dotenv').config();
require('./utils/logging/banner.js');

const client = new Client({
    allowedMentions: {
        parse: ['users', 'roles', 'everyone'],
        repliedUser: true
    },
    partials: [
        Partials.Channel,
        Partials.GuildMember,
        Partials.Message,
        Partials.Reaction,
        Partials.User,
        Partials.GuildScheduledEvent
    ],
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildWebhooks,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMessageTyping,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.DirectMessageReactions,
        GatewayIntentBits.DirectMessageTyping,
        GatewayIntentBits.GuildScheduledEvents,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildPresences
    ]
});
client.logs = logger;
client.logs.bannerShown = true;
client.config = require('./config/config');
client.webhooks = require('./config/webhooks.json');
const webHooksArray = ['startLogs', 'consoleLogs', 'errorLogs'];
if (process.env.WEBHOOK_ID && process.env.WEBHOOK_TOKEN) {
    for (const webhookName of webHooksArray) {
        client.webhooks[webhookName].id = process.env.WEBHOOK_ID;
        client.webhooks[webhookName].token = process.env.WEBHOOK_TOKEN;
    }
}

client.application_commands = new Map();
client.contextMenu_commands = new Map();
client.prefix_commands = new Map();
client.cooldowns = new Map();

fs.readdirSync('./handlers').forEach(dir => {
    fs.readdirSync(`./handlers/${dir}`)
        .filter(file => file.endsWith('.js'))
        .forEach(handler => require(`./handlers/${dir}/${handler}`)(client));
});

client.on('ready', () => {
    console.log(`Logged in as ${client.user.tag}!`);
    // Ensure the bot is ready before handling interactions
});

client.on('messageCreate', (message) => {
    // Ignore messages from bots, @everyone, @here, and role mentions
    if (message.author.bot || message.mentions.everyone || message.mentions.roles.size > 0) return;

    // Respond only when the bot is directly mentioned
    if (message.mentions.has(client.user)) {
        const inviteLink = process.env.INVITE || `https://discord.com/api/oauth2/authorize?client_id=${client.user.id}&permissions=8&scope=bot%20applications.commands`;
        const supportServer = process.env.SUPPORT || 'https://your-support-link.com';

        const embed = new EmbedBuilder()
            .setTitle(`👋 Hi, I'm ${client.user.username}`) // Dynamically uses the bot's username
            .setDescription('Use me with slash commands via Discord!')
            .addFields(
                { name: '📨 ┆ Invite Me', value: `[Invite the bot to your server](${inviteLink})` },
                { name: '❓ ┇ I don\'t see any slash commands', value: `(${client.user.username}) may not have permissions. Reopen the invite link, select your server, and ensure the bot gets all necessary permissions.` },
                { name: '❓ ┆ Need support?', value: `For questions, you can join our [Support Server](${supportServer}).` },
                { name: '🐞 ┆ Found a bug?', value: 'Report all bugs using: `/report bug`' }
            )
            .setColor('#5865F2')
            .setFooter({ text: `${client.user.username} • Discord Bot` });

        const buttons = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setLabel('📨 Invite Me')
                .setStyle(ButtonStyle.Link)
                .setURL(inviteLink),
            new ButtonBuilder()
                .setLabel('❓ Support Server')
                .setStyle(ButtonStyle.Link)
                .setURL(supportServer)
        );

        return message.reply({ embeds: [embed], components: [buttons] });
    }
});

client.login(client.config.client.token);

module.exports = { client };
