const Settings = require('../../schemas/settings');
const RestrictionUserData = require('../../schemas/restrictionUserData');

module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'guildMemberRemove'
};

module.exports.eventRun = async (member, client) => {
    try {
        // Get guild settings to check for restriction role
        const guildSettings = await Settings.findOne({ guildId: member.guild.id });
        if (!guildSettings || !guildSettings.restrictionRoleId) {
            return; // No restriction role configured
        }

        // Check if the leaving member had the restriction role
        const restrictionRole = member.guild.roles.cache.get(guildSettings.restrictionRoleId);
        if (!restrictionRole) {
            return; // Restriction role doesn't exist
        }

        // Check if the member had the restriction role
        const hadRestrictionRole = member.roles.cache.has(guildSettings.restrictionRoleId);
        
        if (hadRestrictionRole) {
            console.log(`🚫 User ${member.user.tag} (${member.user.id}) left while restricted in ${member.guild.name}`);

            try {
                // Auto-ban the user
                await member.guild.members.ban(member.user.id, {
                    reason: 'Left during restriction - uncooperative with investigation'
                });

                console.log(`🔨 Auto-banned ${member.user.tag} for leaving during restriction`);

                // Update their restriction record to reflect they left and were banned
                await RestrictionUserData.findOneAndUpdate(
                    { 
                        userId: member.user.id, 
                        serverId: member.guild.id, 
                        isActive: true 
                    },
                    { 
                        $set: { 
                            leftDuringRestriction: true,
                            autoBanned: true,
                            autoBanReason: 'Left during restriction - uncooperative with investigation',
                            autoBanDate: new Date()
                        }
                    }
                );

                // Log to console and potentially webhook
                const logMessage = `🚫 **Auto-Ban Executed**\n` +
                    `**User:** ${member.user.tag} (${member.user.id})\n` +
                    `**Guild:** ${member.guild.name} (${member.guild.id})\n` +
                    `**Reason:** Left during restriction - uncooperative with investigation\n` +
                    `**Action:** Automatically banned for leaving while restricted`;

                console.log(logMessage);

                // Try to log to the guild's log channel if configured
                if (guildSettings.logChannelId) {
                    const logChannel = member.guild.channels.cache.get(guildSettings.logChannelId);
                    if (logChannel && logChannel.permissionsFor(client.user).has('SendMessages')) {
                        const { EmbedBuilder } = require('discord.js');
                        const embed = new EmbedBuilder()
                            .setTitle('🚫 Auto-Ban: Left During Restriction')
                            .setColor(0xFF0000)
                            .addFields(
                                { name: 'User', value: `${member.user.tag} (${member.user.id})`, inline: true },
                                { name: 'Action', value: 'Automatically banned', inline: true },
                                { name: 'Reason', value: 'Left during restriction - uncooperative with investigation', inline: false },
                                { name: 'Policy', value: 'Users who leave while restricted are automatically banned to prevent evasion', inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Restriction Enforcement System' });

                        await logChannel.send({ embeds: [embed] });
                    }
                }

                // Emit restriction update event to update production stats
                client.emit('restrictionUpdate', member.guild.id);

            } catch (banError) {
                console.error(`Failed to auto-ban ${member.user.tag}:`, banError);
                
                // Still log the attempt even if ban failed
                if (guildSettings.logChannelId) {
                    const logChannel = member.guild.channels.cache.get(guildSettings.logChannelId);
                    if (logChannel && logChannel.permissionsFor(client.user).has('SendMessages')) {
                        const { EmbedBuilder } = require('discord.js');
                        const embed = new EmbedBuilder()
                            .setTitle('⚠️ Auto-Ban Failed: Left During Restriction')
                            .setColor(0xFFA500)
                            .addFields(
                                { name: 'User', value: `${member.user.tag} (${member.user.id})`, inline: true },
                                { name: 'Action', value: 'Ban attempt failed', inline: true },
                                { name: 'Reason', value: 'Left during restriction - uncooperative with investigation', inline: false },
                                { name: 'Error', value: banError.message || 'Unknown error', inline: false },
                                { name: 'Manual Action Required', value: 'Staff should manually ban this user', inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Restriction Enforcement System' });

                        await logChannel.send({ embeds: [embed] });
                    }
                }
            }
        }

    } catch (error) {
        console.error('Error in guildMemberRemove event:', error);
    }
};
