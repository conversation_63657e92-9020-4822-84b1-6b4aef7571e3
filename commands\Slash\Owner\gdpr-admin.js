const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('gdpr-admin')
            .setDescription('GDPR administration commands (Owner only)')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('blacklisted-users')
                    .setDescription('View all GDPR blacklisted users')
                    .addIntegerOption(option =>
                        option.setName('limit')
                            .setDescription('Number of users to show (default: 10, max: 50)')
                            .setRequired(false)
                            .setMinValue(1)
                            .setMaxValue(50)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('restricted-users')
                    .setDescription('View all GDPR protected restricted users')
                    .addIntegerOption(option =>
                        option.setName('limit')
                            .setDescription('Number of users to show (default: 10, max: 50)')
                            .setRequired(false)
                            .setMinValue(1)
                            .setMaxValue(50)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('user-details')
                    .setDescription('View detailed GDPR information for a specific user')
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user to check GDPR details for')
                            .setRequired(true)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('statistics')
                    .setDescription('View GDPR system statistics')),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is an owner
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (!ownerIds.includes(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to bot owners only.',
            ephemeral: true
        });
    }

    const subcommand = interaction.options.getSubcommand();
    const limit = interaction.options.getInteger('limit') || 10;

    try {
        await interaction.deferReply({ ephemeral: true });

        if (subcommand === 'blacklisted-users') {
            const LocalBlacklist = require('../../../models/LocalBlacklist');
            const gdprBlacklisted = await LocalBlacklist.find({
                type: 'gdpr',
                guildId: 'global'
            }).sort({ blacklistedAt: -1 }).limit(limit);

            if (gdprBlacklisted.length === 0) {
                return interaction.editReply({
                    content: '📋 No GDPR blacklisted users found.'
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('🔒 GDPR Blacklisted Users')
                .setColor(0x800080)
                .setDescription(`**Total GDPR Blacklisted Users:** ${gdprBlacklisted.length}`)
                .setFooter({ text: `Showing ${gdprBlacklisted.length} users | Owner Admin Panel` })
                .setTimestamp();

            let description = '';
            for (let i = 0; i < gdprBlacklisted.length; i++) {
                const user = gdprBlacklisted[i];
                const blacklistedDate = new Date(user.blacklistedAt);
                const timestamp = `<t:${Math.floor(blacklistedDate.getTime() / 1000)}:R>`;
                
                description += `**${i + 1}.** <@${user.userId}> (\`${user.userId}\`)\n`;
                description += `├ **Blacklisted:** ${timestamp}\n`;
                description += `├ **By:** <@${user.blacklistedBy}>\n`;
                description += `└ **Reason:** ${user.reason.substring(0, 100)}${user.reason.length > 100 ? '...' : ''}\n\n`;
            }

            embed.setDescription(description);
            await interaction.editReply({ embeds: [embed] });

        } else if (subcommand === 'restricted-users') {
            const RestrictionUserData = require('../../../schemas/restrictionUserData');
            const gdprRestricted = await RestrictionUserData.find({
                gdprProtected: true
            }).sort({ createdAt: -1 }).limit(limit);

            if (gdprRestricted.length === 0) {
                return interaction.editReply({
                    content: '📋 No GDPR protected restricted users found.'
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('🔒 GDPR Protected Restricted Users')
                .setColor(0x9B59B6)
                .setDescription(`**GDPR Protected Restrictions**`)
                .setFooter({ text: `Showing ${gdprRestricted.length} restrictions | Owner Admin Panel` })
                .setTimestamp();

            let description = '';
            for (let i = 0; i < gdprRestricted.length; i++) {
                const restriction = gdprRestricted[i];
                const restrictedDate = new Date(restriction.createdAt);
                const timestamp = `<t:${Math.floor(restrictedDate.getTime() / 1000)}:R>`;
                
                description += `**${i + 1}.** <@${restriction.userId}> (\`${restriction.userId}\`)\n`;
                description += `├ **Server:** ${restriction.serverName}\n`;
                description += `├ **Reason:** ${restriction.restrictionReason}\n`;
                description += `├ **Status:** ${restriction.isActive ? '🔴 Active' : '🟢 Inactive'}\n`;
                description += `├ **Appealable:** ${restriction.isAppealable ? '✅ Yes' : '❌ No'}\n`;
                description += `├ **Date:** ${timestamp}\n`;
                description += `└ **Code:** \`${restriction.uniqueCode}\`\n\n`;
            }

            embed.setDescription(description);
            await interaction.editReply({ embeds: [embed] });

        } else if (subcommand === 'user-details') {
            const targetUser = interaction.options.getUser('user');
            
            // Check GDPR blacklist
            const LocalBlacklist = require('../../../models/LocalBlacklist');
            const gdprBlacklist = await LocalBlacklist.findOne({
                userId: targetUser.id,
                type: 'gdpr'
            });

            // Check GDPR restrictions
            const RestrictionUserData = require('../../../schemas/restrictionUserData');
            const gdprRestrictions = await RestrictionUserData.find({
                userId: targetUser.id,
                gdprProtected: true
            }).sort({ createdAt: -1 });

            // Check GDPR logs
            const GDPRLog = require('../../../schemas/gdprLog');
            const gdprLogs = await GDPRLog.find({
                user_id: targetUser.id
            }).sort({ timestamp: -1 }).limit(5);

            const embed = new EmbedBuilder()
                .setTitle('🔍 GDPR User Details')
                .setDescription(`**Complete GDPR information for:** ${targetUser.tag} (\`${targetUser.id}\`)`)
                .setColor(0x800080)
                .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
                .setFooter({ text: 'Owner Admin Panel - GDPR Details' })
                .setTimestamp();

            // GDPR Blacklist Status
            if (gdprBlacklist) {
                const blacklistDate = new Date(gdprBlacklist.blacklistedAt);
                embed.addFields({
                    name: '🚫 GDPR Blacklist Status',
                    value: `**Status:** 🔴 GDPR Blacklisted\n**Date:** <t:${Math.floor(blacklistDate.getTime() / 1000)}:F>\n**By:** <@${gdprBlacklist.blacklistedBy}>\n**Reason:** ${gdprBlacklist.reason.substring(0, 200)}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '🚫 GDPR Blacklist Status',
                    value: '**Status:** ✅ Not GDPR Blacklisted',
                    inline: false
                });
            }

            // GDPR Restrictions
            if (gdprRestrictions.length > 0) {
                let restrictionText = '';
                gdprRestrictions.slice(0, 3).forEach((restriction, index) => {
                    const date = new Date(restriction.createdAt);
                    restrictionText += `**${index + 1}.** ${restriction.serverName}\n`;
                    restrictionText += `├ **Reason:** ${restriction.restrictionReason}\n`;
                    restrictionText += `├ **Status:** ${restriction.isActive ? '🔴 Active' : '🟢 Inactive'}\n`;
                    restrictionText += `├ **Date:** <t:${Math.floor(date.getTime() / 1000)}:R>\n`;
                    restrictionText += `└ **Code:** \`${restriction.uniqueCode}\`\n\n`;
                });
                
                if (gdprRestrictions.length > 3) {
                    restrictionText += `*...and ${gdprRestrictions.length - 3} more restrictions*`;
                }

                embed.addFields({
                    name: `🔒 GDPR Protected Restrictions (${gdprRestrictions.length})`,
                    value: restrictionText,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '🔒 GDPR Protected Restrictions',
                    value: '**Status:** ✅ No GDPR protected restrictions',
                    inline: false
                });
            }

            // GDPR Logs
            if (gdprLogs.length > 0) {
                let logText = '';
                gdprLogs.forEach((log, index) => {
                    const date = new Date(log.timestamp);
                    logText += `**${index + 1}.** ${log.reason}\n`;
                    logText += `├ **Flags:** ${log.flags.join(', ')}\n`;
                    logText += `├ **Issuer:** <@${log.Issuer}>\n`;
                    logText += `└ **Date:** <t:${Math.floor(date.getTime() / 1000)}:R>\n\n`;
                });

                embed.addFields({
                    name: `📋 GDPR Logs (${gdprLogs.length})`,
                    value: logText,
                    inline: false
                });
            }

            await interaction.editReply({ embeds: [embed] });

        } else if (subcommand === 'statistics') {
            const LocalBlacklist = require('../../../models/LocalBlacklist');
            const RestrictionUserData = require('../../../schemas/restrictionUserData');
            const GDPRLog = require('../../../schemas/gdprLog');

            // Get statistics
            const totalGdprBlacklisted = await LocalBlacklist.countDocuments({ type: 'gdpr' });
            const totalGdprRestrictions = await RestrictionUserData.countDocuments({ gdprProtected: true });
            const activeGdprRestrictions = await RestrictionUserData.countDocuments({ gdprProtected: true, isActive: true });
            const totalGdprLogs = await GDPRLog.countDocuments({});

            // Recent activity (last 7 days)
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            
            const recentGdprBlacklists = await LocalBlacklist.countDocuments({
                type: 'gdpr',
                blacklistedAt: { $gte: sevenDaysAgo }
            });

            const recentGdprRestrictions = await RestrictionUserData.countDocuments({
                gdprProtected: true,
                createdAt: { $gte: sevenDaysAgo }
            });

            const embed = new EmbedBuilder()
                .setTitle('📊 GDPR System Statistics')
                .setColor(0x800080)
                .setDescription('**Complete GDPR system overview**')
                .addFields(
                    { name: '🚫 Total GDPR Blacklisted', value: `\`${totalGdprBlacklisted}\` users`, inline: true },
                    { name: '🔒 Total GDPR Restrictions', value: `\`${totalGdprRestrictions}\` restrictions`, inline: true },
                    { name: '🔴 Active GDPR Restrictions', value: `\`${activeGdprRestrictions}\` active`, inline: true },
                    { name: '📋 Total GDPR Logs', value: `\`${totalGdprLogs}\` log entries`, inline: true },
                    { name: '📅 Recent Blacklists (7d)', value: `\`${recentGdprBlacklists}\` new`, inline: true },
                    { name: '📅 Recent Restrictions (7d)', value: `\`${recentGdprRestrictions}\` new`, inline: true }
                )
                .setFooter({ text: 'Owner Admin Panel - GDPR Statistics' })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        }

    } catch (error) {
        console.error('Error in GDPR admin command:', error);
        await interaction.editReply({
            content: '❌ An error occurred while processing the GDPR admin command.'
        });
    }
};
