const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('fix-server-data')
            .setDescription('Fix missing server names and IDs in restriction records (Owner only)')
            .addBooleanOption(option =>
                option.setName('dry_run')
                    .setDescription('Preview changes without applying them (default: true)')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is an owner
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (!ownerIds.includes(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to bot owners only.',
            flags: 64 // Ephemeral flag
        });
    }

    const dryRun = interaction.options.getBoolean('dry_run') ?? true;

    await interaction.deferReply({ flags: 64 }); // Ephemeral deferred reply

    try {
        const RestrictionUserData = require('../../../schemas/restrictionUserData');

        // Find all restriction records with missing or invalid server data
        const problematicRecords = await RestrictionUserData.find({
            $or: [
                { serverName: { $exists: false } },
                { serverName: null },
                { serverName: '' },
                { serverName: 'Unknown Server' },
                { serverId: { $exists: false } },
                { serverId: null },
                { serverId: '' },
                { serverId: 'Unknown ID' },
                { serverId: 'UNKNOWN_SERVER_ID' }
            ]
        });

        if (problematicRecords.length === 0) {
            return interaction.editReply({
                content: '✅ All restriction records have proper server information!'
            });
        }

        let fixedCount = 0;
        let unfixableCount = 0;
        const fixedServers = new Set();
        const unfixableRecords = [];

        for (const record of problematicRecords) {
            try {
                // Try to get the guild from the bot's cache
                const guild = interaction.client.guilds.cache.get(record.serverId);
                
                if (guild) {
                    // We can fix this record
                    if (!dryRun) {
                        await RestrictionUserData.findByIdAndUpdate(record._id, {
                            serverName: guild.name,
                            serverId: guild.id
                        });
                    }
                    fixedCount++;
                    fixedServers.add(`${guild.name} (${guild.id})`);
                } else {
                    // We can't fix this record - guild not in cache
                    unfixableCount++;
                    unfixableRecords.push({
                        userId: record.userId,
                        serverId: record.serverId || 'Unknown',
                        serverName: record.serverName || 'Unknown',
                        createdAt: record.createdAt
                    });
                }
            } catch (error) {
                console.error(`Error processing record ${record._id}:`, error);
                unfixableCount++;
            }
        }

        // Create result embed
        const embed = new EmbedBuilder()
            .setTitle(dryRun ? '🔍 Server Data Fix Preview' : '✅ Server Data Fix Complete')
            .setColor(dryRun ? 0xFFA500 : 0x00FF00)
            .addFields(
                { name: '📊 Total Records Found', value: `${problematicRecords.length} records`, inline: true },
                { name: '✅ Fixable Records', value: `${fixedCount} records`, inline: true },
                { name: '❌ Unfixable Records', value: `${unfixableCount} records`, inline: true }
            )
            .setFooter({ text: dryRun ? 'Run with dry_run:false to apply changes' : 'Server Data Fix Complete' })
            .setTimestamp();

        if (dryRun) {
            embed.setDescription('**Preview Mode**: No changes have been applied yet.');
        } else {
            embed.setDescription('**Changes Applied**: Server data has been updated.');
        }

        // Add fixed servers info
        if (fixedServers.size > 0) {
            const serverList = Array.from(fixedServers).slice(0, 10).join('\n');
            const moreServers = fixedServers.size > 10 ? `\n...and ${fixedServers.size - 10} more servers` : '';
            embed.addFields({
                name: `🔧 ${dryRun ? 'Would Fix' : 'Fixed'} Servers`,
                value: serverList + moreServers,
                inline: false
            });
        }

        // Add unfixable records info
        if (unfixableRecords.length > 0) {
            const unfixableList = unfixableRecords.slice(0, 5).map(record => 
                `User: ${record.userId} | Server: ${record.serverId} | Date: ${new Date(record.createdAt).toLocaleDateString()}`
            ).join('\n');
            const moreUnfixable = unfixableRecords.length > 5 ? `\n...and ${unfixableRecords.length - 5} more records` : '';
            
            embed.addFields({
                name: '❌ Unfixable Records (Bot not in these servers)',
                value: unfixableList + moreUnfixable,
                inline: false
            });
        }

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error in fix-server-data command:', error);
        await interaction.editReply({
            content: '❌ An error occurred while processing server data fix.'
        });
    }
};
