const mongoose = require('mongoose');

const localBlacklistSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    guildId: { type: String, required: true },
    reason: { type: String, required: true },
    blacklistedBy: { type: String, required: true },
    blacklistedAt: { type: Date, default: Date.now },
    type: { type: String, enum: ['user', 'guild', 'gdpr'], required: true }
});

// Create compound index for efficient lookups
localBlacklistSchema.index({ userId: 1, guildId: 1 }, { unique: true });

module.exports = mongoose.model('LocalBlacklist', localBlacklistSchema);
