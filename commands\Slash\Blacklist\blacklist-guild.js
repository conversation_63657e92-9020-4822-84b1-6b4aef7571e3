const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { addToLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('blacklist-guild')
            .setDescription('Blacklist a guild from using the bot (Developer only)')
            .addStringOption(option =>
                option.setName('guild_id')
                    .setDescription('The guild ID to blacklist')
                    .setRequired(true))
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('Reason for blacklisting')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const guildId = interaction.options.getString('guild_id');
    const reason = interaction.options.getString('reason');

    // Prevent blacklisting current guild if command is run in a guild
    if (interaction.guild && guildId === interaction.guild.id) {
        return interaction.reply({
            content: '❌ You cannot blacklist the current guild.',
            ephemeral: true
        });
    }

    try {
        const result = await addToLocalBlacklist(
            guildId,
            guildId,
            reason,
            interaction.user.id,
            'guild'
        );

        if (result.success) {
            // Check if bot is currently in the blacklisted guild
            const targetGuild = interaction.client.guilds.cache.get(guildId);
            
            // Log the guild blacklist action
            sendBlacklistLog('GUILD_BLACKLISTED', {
                guildName: targetGuild?.name || 'Unknown',
                guildId: guildId,
                blacklistedBy: interaction.user.tag,
                blacklistedById: interaction.user.id,
                reason: reason,
                autoLeft: !!targetGuild
            });

            const embed = new EmbedBuilder()
                .setTitle('✅ Guild Blacklisted')
                .setColor(0xFF0000)
                .addFields(
                    { name: 'Guild ID', value: guildId, inline: true },
                    { name: 'Reason', value: reason, inline: true },
                    { name: 'Blacklisted by', value: `${interaction.user}`, inline: true }
                )
                .setTimestamp();

            if (targetGuild) {
                embed.addFields({ name: 'Action', value: '🚪 Bot will leave the guild immediately', inline: false });
                
                await interaction.reply({ embeds: [embed] });
                
                // Try to notify the guild before leaving
                try {
                    if (targetGuild.systemChannel && targetGuild.systemChannel.permissionsFor(interaction.client.user).has('SendMessages')) {
                        const leaveEmbed = new EmbedBuilder()
                            .setTitle('🚫 Bot Removed')
                            .setDescription('This server has been blacklisted from using this bot.')
                            .setColor(0xFF0000)
                            .addFields(
                                { name: 'Reason', value: reason, inline: false },
                                { name: 'Contact', value: 'Contact the bot developers if you believe this is an error.', inline: false }
                            )
                            .setTimestamp();

                        await targetGuild.systemChannel.send({ embeds: [leaveEmbed] });
                    }
                } catch (error) {
                    console.error('Failed to send leave notification:', error);
                }
                
                // Leave the guild
                await targetGuild.leave();
                console.log(`🚫 Left blacklisted guild: ${targetGuild.name} (${guildId})`);
            } else {
                await interaction.reply({ embeds: [embed] });
            }
        } else {
            await interaction.reply({
                content: `❌ Failed to blacklist guild: ${result.error}`,
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error blacklisting guild:', error);
        await interaction.reply({
            content: '❌ An error occurred while blacklisting the guild.',
            ephemeral: true
        });
    }
};
