const { <PERSON><PERSON><PERSON><PERSON><PERSON>, TextInputBuilder, ActionRowBuilder, TextInputStyle, ButtonBuilder, ButtonStyle, EmbedBuilder } = require('discord.js'); // Ensure EmbedBuilder is imported
const RestrictionUserData = require('../../schemas/restrictionUserData');
const Settings = require('../../schemas/settings');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Button: { 
        enabled: true, 
        id: ['appeal_ban', 'ban_information', 'accept_appeal', 'decline_appeal']
    }
};

module.exports.help = {
    Button: { 
        id: ['appeal_ban', 'ban_information', 'accept_appeal', 'decline_appeal'] 
    }
};

module.exports.buttonRun = async (interaction, client) => {
    // Handle appeal ban
    if (interaction.customId === 'appeal_ban') {
        const userRestriction = await RestrictionUserData.findOne({ userId: interaction.user.id, isActive: true });
        if (!userRestriction) {
            return interaction.reply({ 
                content: "You are not restricted from this server, so these buttons are not applicable to you.", 
                ephemeral: true 
            });
        }
        
        if (!userRestriction.isAppealable) {
            return interaction.reply({ 
                content: "Your restriction is not appealable.", 
                ephemeral: true 
            });
        }
        
        const modal = new ModalBuilder().setCustomId('appeal_ban_modal').setTitle('Ban Appeal');
        
        const question1 = new TextInputBuilder()
            .setCustomId('reason_unban')
            .setLabel('Why should you be unbanned?') 
            .setStyle(TextInputStyle.Paragraph);
        
        const question2 = new TextInputBuilder()
            .setCustomId('side_of_story')
            .setLabel('What is your side of the story?') 
            .setStyle(TextInputStyle.Paragraph);
        
        const question3 = new TextInputBuilder()
            .setCustomId('prevent_future')
            .setLabel('How will you avoid this again?') 
            .setStyle(TextInputStyle.Paragraph);
        
        modal.addComponents(
            new ActionRowBuilder().addComponents(question1),
            new ActionRowBuilder().addComponents(question2),
            new ActionRowBuilder().addComponents(question3)
        );
        
        await interaction.showModal(modal);

        // DM the user that their appeal has been submitted
        try {
            const dmMessage = `Your appeal in ${interaction.guild.name} has been submitted. Please allow up to 24 hours for a reply.`;
            await interaction.user.send(dmMessage);
        } catch (err) {
            console.error('Could not send DM to the user:', err);
        }
    }

    // Handle ban information
    if (interaction.customId === 'ban_information') {
        const userRestriction = await RestrictionUserData.findOne({ userId: interaction.user.id, isActive: true });
        if (!userRestriction) {
            return interaction.reply({ 
                content: "You are not restricted from this server, so these buttons are not applicable to you.", 
                ephemeral: true 
            });
        }

        const embed = client.embed()
            .setTitle('Ban Information')
            .setColor('#ff0000')
            .addFields(
                { name: 'Is Appealable', value: userRestriction.isAppealable ? 'Yes' : 'No', inline: true },
                { name: 'Restriction Reason', value: userRestriction.restrictionReason || 'No reason provided', inline: true }
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` });

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Handle accept appeal or decline appeal
    if (interaction.customId === 'accept_appeal' || interaction.customId === 'decline_appeal') {
        await interaction.deferReply({ ephemeral: true });
        const settings = await Settings.findOne({ guildId: interaction.guild.id });
        const staffRoleId = settings?.staffRoleId;
        const ownerIds = process.env.owners ? process.env.owners.split(',') : [];

        if (!staffRoleId) {
            return interaction.followUp({
                content: 'Staff role is not configured. Please set it up using the settings command.',
                ephemeral: true,
            });
        }

        const member = await interaction.guild.members.fetch(interaction.user.id);

        // Check if the user has the required staff role or is an owner
        if (!member.roles.cache.has(staffRoleId) && !ownerIds.includes(interaction.user.id)) {
            return interaction.followUp({
                content: 'You do not have the required permissions to interact with these buttons.',
                ephemeral: true,
            });
        }

        const restrictionRoleId = settings?.restrictionRoleId;
        const logChannelId = settings?.logChannelId;

        if (!restrictionRoleId) {
            return interaction.followUp({
                content: 'Restriction role is not configured. Please set it up using the settings command.',
                ephemeral: true,
            });
        }

        const memberToUnrestrict = await interaction.guild.members.fetch(interaction.message.embeds[0].footer.text).catch(() => null);

        if (!memberToUnrestrict) {
            return interaction.followUp({
                content: 'The user is no longer in the server.',
                ephemeral: true,
            });
        }

        // Check if the restriction role exists in the server
        const restrictionRole = interaction.guild.roles.cache.get(restrictionRoleId);
        if (!restrictionRole) {
            return interaction.followUp({
                content: 'The restriction role no longer exists in this server. Please update the restriction role in the settings.',
                ephemeral: true,
            });
        }

        const message = interaction.message;
        const originalEmbed = message.embeds[0]; // Fetch the original embed

        // Recreate the embed as an instance of EmbedBuilder
        const embed = EmbedBuilder.from(originalEmbed);

        if (interaction.customId === 'accept_appeal') {
            // Remove the restriction role
            await memberToUnrestrict.roles.remove(restrictionRole).catch(console.error);

            // Restore previous roles
            const restrictionData = await RestrictionUserData.findOne({ userId: memberToUnrestrict.id, isActive: true });
            if (restrictionData) {
                restrictionData.serverId = interaction.guild.id; // Ensure serverId is included
                restrictionData.serverName = interaction.guild.name; // Ensure serverName is included
                restrictionData.isActive = false;
                await restrictionData.save();

                if (restrictionData.roles && restrictionData.roles.length > 0) {
                    for (const roleId of restrictionData.roles) {
                        const role = interaction.guild.roles.cache.get(roleId);
                        if (role) {
                            await memberToUnrestrict.roles.add(role).catch(console.error);
                        }
                    }
                }
            }

            // Emit custom event to update production stats
            client.emit('restrictionUpdate', interaction.guild.id);

            // Update the embed
            embed.setColor('#00ff00').setTitle('Restriction Appeal Accepted'); // Update title and color
            await message.edit({ embeds: [embed], components: [] }); // Remove buttons

            // Log the acceptance of the appeal in the logging channel
            if (logChannelId) {
                const logChannel = await client.channels.fetch(logChannelId).catch(() => null);
                if (logChannel) {
                    const logEmbed = client.embed()
                        .setTitle('Appeal Accepted')
                        .setDescription('A restriction appeal has been accepted.')
                        .setColor('#00ff00')
                        .addFields(
                            { name: 'User', value: `${memberToUnrestrict.user.tag} (${memberToUnrestrict.id})`, inline: true },
                            { name: 'Staff', value: `${interaction.user.tag} (${interaction.user.id})`, inline: true },
                            { name: 'Reason for Restriction', value: restrictionData?.restrictionReason || 'No reason provided', inline: true },
                            { name: 'Date and Time', value: new Date().toLocaleString(), inline: true }
                        )
                        .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
                        .setTimestamp();
                    await logChannel.send({ embeds: [logEmbed] });
                }
            }

            await interaction.followUp({ content: 'The appeal has been accepted and logged.', ephemeral: true });

            // DM the user that their appeal has been accepted
            try {
                const dmMessage = `Your appeal for ${interaction.guild.name} has been accepted. You may now talk in the server.`;
                await memberToUnrestrict.user.send(dmMessage);
            } catch (err) {
                console.error('Could not send DM to the user:', err);
            }
        } else if (interaction.customId === 'decline_appeal') {
            const restrictionData = await RestrictionUserData.findOne({ userId: interaction.user.id, isActive: true });
            if (restrictionData) {
                restrictionData.serverId = interaction.guild.id; // Ensure serverId is included
                restrictionData.serverName = interaction.guild.name; // Ensure serverName is included
                restrictionData.isActive = false;
                await restrictionData.save();
            }

            // Emit custom event to update production stats
            client.emit('restrictionUpdate', interaction.guild.id);

            // Update the embed
            embed.setColor('#ff0000').setTitle('Restriction Appeal Declined'); // Update title and color
            await message.edit({ embeds: [embed], components: [] }); // Remove buttons

            await interaction.followUp({ content: 'The appeal has been declined and logged.', ephemeral: true });

            // DM the user that their appeal has been declined
            try {
                const dmMessage = `Your appeal in ${interaction.guild.name} has been declined. You may re-try soon.`;
                await memberToUnrestrict.user.send(dmMessage);
            } catch (err) {
                console.error('Could not send DM to the user:', err);
            }
        }
    }
};
