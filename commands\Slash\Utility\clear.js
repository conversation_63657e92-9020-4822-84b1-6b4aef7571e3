const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();
const Settings = require('../../../schemas/settings'); // Corrected import path

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['ManageGuild'], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('clear')
            .setDescription('Delete a number of messages from the channel')
            .addIntegerOption(option =>
                option.setName('amount')
                    .setDescription('Number of messages to delete (1–100)')
                    .setRequired(true)
            )
            .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages), // Ensure this is a string
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
        if (ownerIds.includes(interaction.user.id)) {
            // Bypass all permission checks for owners
        } else {
            const settings = await Settings.findOne({ guildId: interaction.guild.id });
            const staffRoleId = settings?.staffRoleId;

            if (!staffRoleId) {
                return interaction.reply({
                    content: '❌ The staff role is not set up. Please use `/setstaffrole` to configure it.',
                    ephemeral: true,
                });
            }

            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return interaction.reply({
                    content: '❌ You do not have the required staff role to use this command.',
                    ephemeral: true,
                });
            }
        }

        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply({ ephemeral: true });
        }

        const amount = interaction.options.getInteger('amount');

        if (amount < 1 || amount > 100) {
            return await interaction.editReply({
                content: '❌ You must specify a number between 1 and 100.',
            });
        }

        const messages = await interaction.channel.bulkDelete(amount, true);
        await interaction.editReply({
            content: `✅ Successfully deleted ${messages.size} messages.`,
        });

    } catch (error) {
        console.error('Error executing clear command:', error);
        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while trying to delete messages.',
                    ephemeral: true,
                });
            } catch (replyError) {
                console.error('Failed to send fallback reply:', replyError);
            }
        }
    }
};
