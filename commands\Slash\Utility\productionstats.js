const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const RestrictionUserData = require('../../../schemas/restrictionUserData');
const Settings = require('../../../schemas/settings');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['Administrator'],
        botPermissions: [],
        isDefaultCooldown: 5,
        isPremiumCooldown: 3,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('productionstats')
            .setDescription('Display production statistics including restricted user counts.')
            .setDefaultMemberPermissions('0'), // Administrator only
    },
};

module.exports.slashRun = async (interaction, client) => {
    // Check if production stats are enabled
    const showProductionStats = process.env.SHOW_PRODUCTION_STATS === 'true';
    
    if (!showProductionStats) {
        return interaction.reply({
            content: '❌ Production statistics are currently disabled.',
            ephemeral: true,
        });
    }

    try {
        await interaction.deferReply();

        // Get guild settings
        const guildSettings = await Settings.findOne({ guildId: interaction.guild.id });

        // Get restriction role info and count actual role members
        const restrictionRoleId = guildSettings?.restrictionRoleId;
        let totalActiveRestrictions = 0;
        let restrictionRoleName = 'Not Set';

        if (restrictionRoleId) {
            const restrictionRole = interaction.guild.roles.cache.get(restrictionRoleId);
            if (restrictionRole) {
                restrictionRoleName = restrictionRole.name;
                totalActiveRestrictions = restrictionRole.members.size;
            }
        }

        // Count total restrictions (active + inactive) in database for historical data
        const totalRestrictions = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id
        });

        // Count appealable restrictions from database (for historical context)
        const appealableRestrictions = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id,
            isActive: true,
            isAppealable: true
        });

        // Count non-appealable restrictions from database (for historical context)
        const nonAppealableRestrictions = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id,
            isActive: true,
            isAppealable: false
        });

        // Get recent restrictions (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const recentRestrictions = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id,
            createdAt: { $gte: sevenDaysAgo }
        });

        // Get auto-ban statistics
        const totalAutoBans = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id,
            autoBanned: true
        });

        const recentAutoBans = await RestrictionUserData.countDocuments({
            serverId: interaction.guild.id,
            autoBanned: true,
            autoBanDate: { $gte: sevenDaysAgo }
        });

        // Get guild member count
        const guildMemberCount = interaction.guild.memberCount;

        // Calculate restriction percentage
        const restrictionPercentage = guildMemberCount > 0 
            ? ((totalActiveRestrictions / guildMemberCount) * 100).toFixed(2)
            : '0.00';

        // Note: restrictionRoleName and totalActiveRestrictions are already set above

        // Create embed
        const embed = new EmbedBuilder()
            .setTitle('📊 Production Statistics')
            .setDescription(`Server restriction statistics for **${interaction.guild.name}**`)
            .setColor(process.env.DEFAULT_COLOUR || '#ADD8E6')
            .addFields(
                {
                    name: '🔒 Active Restrictions',
                    value: `\`${totalActiveRestrictions}\` users`,
                    inline: true
                },
                {
                    name: '📈 Total Restrictions',
                    value: `\`${totalRestrictions}\` all-time`,
                    inline: true
                },
                {
                    name: '📊 Restriction Rate',
                    value: `\`${restrictionPercentage}%\` of members`,
                    inline: true
                },
                {
                    name: '⚖️ Appealable',
                    value: `\`${appealableRestrictions}\` users`,
                    inline: true
                },
                {
                    name: '🚫 Non-Appealable',
                    value: `\`${nonAppealableRestrictions}\` users`,
                    inline: true
                },
                {
                    name: '📅 Recent (7 days)',
                    value: `\`${recentRestrictions}\` new restrictions`,
                    inline: true
                },
                {
                    name: '🔨 Auto-Bans (Total)',
                    value: `\`${totalAutoBans}\` users`,
                    inline: true
                },
                {
                    name: '🔨 Auto-Bans (7 days)',
                    value: `\`${recentAutoBans}\` users`,
                    inline: true
                },
                {
                    name: '👥 Server Members',
                    value: `\`${guildMemberCount}\` total`,
                    inline: true
                },
                {
                    name: '🏷️ Restriction Role',
                    value: `${restrictionRoleName}\n(\`${totalActiveRestrictions}\` members)`,
                    inline: true
                },
                {
                    name: '⚙️ Settings Status',
                    value: guildSettings 
                        ? '✅ Configured' 
                        : '❌ Not configured',
                    inline: true
                }
            )
            .setFooter({ 
                text: `${process.env.copyright || '© Nexoria Development'} | Production Mode`,
                iconURL: interaction.guild.iconURL({ dynamic: true })
            })
            .setTimestamp();

        // Add additional info if in developer mode
        if (process.env.IS_DEVELOPER_MODE === 'true') {
            embed.addFields({
                name: '🔧 Developer Info',
                value: `Maintenance: \`${process.env.IS_MAINTENANCE}\`\nDev Mode: \`${process.env.IS_DEVELOPER_MODE}\``,
                inline: false
            });
        }

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error fetching production statistics:', error);
        
        if (interaction.deferred) {
            await interaction.editReply({
                content: '❌ An error occurred while fetching production statistics.',
            });
        } else {
            await interaction.reply({
                content: '❌ An error occurred while fetching production statistics.',
                ephemeral: true,
            });
        }
    }
};
