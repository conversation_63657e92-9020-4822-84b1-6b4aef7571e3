const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { addToLocalBlacklist, isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');
const { sendGDPRLog } = require('../../../utils/gdprLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('blacklist-gdpr')
            .setDescription('GDPR blacklist a user who requested data deletion (Developer only - PERMANENT)')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to GDPR blacklist')
                    .setRequired(true))
            .addStringOption(option =>
                option.setName('request_details')
                    .setDescription('Details about the GDPR request (optional)')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            flags: 64 // Ephemeral flag
        });
    }

    const targetUser = interaction.options.getUser('user');
    const requestDetails = interaction.options.getString('request_details') || 'GDPR data deletion request';

    // Prevent blacklisting other developers
    if (isDeveloper(targetUser.id)) {
        return interaction.reply({
            content: '❌ Cannot GDPR blacklist other developers.',
            flags: 64 // Ephemeral flag
        });
    }

    // Prevent self-blacklisting
    if (targetUser.id === interaction.user.id) {
        return interaction.reply({
            content: '❌ You cannot GDPR blacklist yourself.',
            flags: 64 // Ephemeral flag
        });
    }

    // Show confirmation dialog with warning
    const confirmationEmbed = new EmbedBuilder()
        .setTitle('⚠️ GDPR Blacklist Confirmation')
        .setDescription('**WARNING: This action is PERMANENT and IRREVERSIBLE**')
        .setColor(0xFF0000)
        .addFields(
            { name: 'Target User', value: `${targetUser} (${targetUser.id})`, inline: true },
            { name: 'Request Type', value: 'GDPR Data Deletion', inline: true },
            { name: 'Scope', value: 'Global (All Servers)', inline: true },
            { name: '🚨 IMPORTANT', value: 'This user will be permanently blocked from:\n• Using any bot commands\n• Being restricted on any server\n• All bot functionality', inline: false },
            { name: '📋 Details', value: requestDetails, inline: false },
            { name: '⚠️ Cannot Be Undone', value: 'GDPR blacklists are permanent and cannot be reversed', inline: false }
        )
        .setFooter({ text: 'Click CONFIRM to proceed or CANCEL to abort' })
        .setTimestamp();

    const confirmButton = new ButtonBuilder()
        .setCustomId('gdpr_confirm')
        .setLabel('CONFIRM GDPR BLACKLIST')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('🚫');

    const cancelButton = new ButtonBuilder()
        .setCustomId('gdpr_cancel')
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('❌');

    const actionRow = new ActionRowBuilder()
        .addComponents(confirmButton, cancelButton);

    await interaction.reply({
        embeds: [confirmationEmbed],
        components: [actionRow],
        flags: 64 // Ephemeral flag
    });

    // Wait for button interaction
    try {
        const buttonInteraction = await interaction.channel.awaitMessageComponent({
            filter: (i) => i.user.id === interaction.user.id && (i.customId === 'gdpr_confirm' || i.customId === 'gdpr_cancel'),
            time: 30000
        });

        // Acknowledge the button interaction immediately
        await buttonInteraction.deferUpdate();

        if (buttonInteraction.customId === 'gdpr_cancel') {
            await buttonInteraction.editReply({
                content: '❌ GDPR blacklist cancelled.',
                embeds: [],
                components: []
            });
            return;
        }

        // Proceed with GDPR blacklist
        const gdprReason = `GDPR Data Deletion Request - ${requestDetails} - PERMANENT BLACKLIST`;
        
        const result = await addToLocalBlacklist(
            targetUser.id,
            'global',
            gdprReason,
            interaction.user.id,
            'gdpr'
        );

        if (result.success) {
            // Log the GDPR blacklist action to webhook
            sendBlacklistLog('GDPR_BLACKLIST', {
                targetUser: targetUser.tag,
                targetUserId: targetUser.id,
                blacklistedBy: interaction.user.tag,
                blacklistedById: interaction.user.id,
                requestDetails: requestDetails,
                reason: gdprReason
            });

            // Log to database with specific GDPR format
            let databaseLogData = null;
            try {
                const GDPRLog = require('../../../schemas/gdprLog');
                const gdprLogEntry = new GDPRLog({
                    user_id: targetUser.id,
                    reason: "GDPR_ERASURE",
                    flags: ["NO_DATA_COLLECTION", "BAN_ALL_SERVERS"],
                    timestamp: new Date().toISOString(),
                    Issuer: interaction.user.id
                });

                await gdprLogEntry.save();
                databaseLogData = gdprLogEntry.toObject();
                console.log(`📋 GDPR database log created for ${targetUser.tag}`);
            } catch (dbError) {
                console.error('Failed to create GDPR database log:', dbError);
            }

            // Send dedicated GDPR webhook log for legal compliance
            if (databaseLogData) {
                sendGDPRLog('GDPR_BLACKLIST_APPLIED', {
                    targetUser: targetUser.tag,
                    targetUserId: targetUser.id,
                    processedBy: interaction.user.tag,
                    processedById: interaction.user.id,
                    requestDetails: requestDetails,
                    databaseLog: databaseLogData
                });
            } else {
                // Log failed attempt for legal audit
                sendGDPRLog('GDPR_BLACKLIST_ATTEMPTED', {
                    targetUser: targetUser.tag,
                    targetUserId: targetUser.id,
                    attemptedBy: interaction.user.tag,
                    attemptedById: interaction.user.id,
                    requestDetails: requestDetails,
                    failureReason: 'Database logging failed'
                });
            }

            const successEmbed = new EmbedBuilder()
                .setTitle('✅ GDPR Blacklist Applied')
                .setColor(0xFF0000)
                .addFields(
                    { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: true },
                    { name: 'Type', value: 'GDPR Data Deletion', inline: true },
                    { name: 'Status', value: 'PERMANENT', inline: true },
                    { name: 'Applied By', value: `${interaction.user}`, inline: true },
                    { name: 'Scope', value: 'Global (All Servers)', inline: true },
                    { name: 'Reversible', value: '❌ NO', inline: true },
                    { name: 'User Message', value: 'User has been notified of GDPR compliance', inline: false },
                    { name: 'Details', value: requestDetails, inline: false }
                )
                .setFooter({ text: 'GDPR Blacklist - Data Deletion Compliance' })
                .setTimestamp();

            await buttonInteraction.editReply({
                embeds: [successEmbed],
                components: []
            });

            // Try to DM the user about their GDPR request being processed
            try {
                const gdprNotificationEmbed = new EmbedBuilder()
                    .setTitle('🔒 GDPR Data Deletion Request Processed')
                    .setDescription('Your request for data deletion under GDPR has been processed.')
                    .setColor(0x5865F2)
                    .addFields(
                        { name: '✅ Data Deletion', value: 'Your data has been marked for deletion in compliance with GDPR', inline: false },
                        { name: '🚫 Bot Access', value: 'You have been permanently blocked from using this bot as requested', inline: false },
                        { name: '📋 Request Details', value: requestDetails, inline: false },
                        { name: '⚠️ Important', value: 'This action is permanent and cannot be reversed', inline: false },
                        { name: '📞 Contact', value: 'If you have questions about this process, contact the bot developers', inline: false }
                    )
                    .setFooter({ text: 'GDPR Compliance - Data Protection' })
                    .setTimestamp();

                await targetUser.send({ embeds: [gdprNotificationEmbed] });
                console.log(`📧 GDPR notification sent to ${targetUser.tag}`);
            } catch (dmError) {
                console.log(`❌ Could not DM GDPR notification to ${targetUser.tag}: ${dmError.message}`);
            }

        } else {
            await buttonInteraction.editReply({
                content: `❌ Failed to apply GDPR blacklist: ${result.error}`,
                embeds: [],
                components: []
            });
        }

    } catch (timeoutError) {
        await interaction.editReply({
            content: '⏰ Confirmation timeout. GDPR blacklist cancelled for safety.',
            embeds: [],
            components: []
        });
    }
};
