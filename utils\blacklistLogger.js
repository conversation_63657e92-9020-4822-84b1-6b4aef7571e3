const { WebhookClient, EmbedBuilder } = require('discord.js');

let blacklistWebhook = null;

// Initialize webhook client
function initializeBlacklistLogger() {
    const webhookUrl = process.env.BLACKLIST_WEBHOOK_URL;
    if (webhookUrl && webhookUrl !== 'your_blacklist_webhook_url_here') {
        try {
            blacklistWebhook = new WebhookClient({ url: webhookUrl });
            console.log('✅ Blacklist webhook logger initialized');
        } catch (error) {
            console.error('❌ Failed to initialize blacklist webhook:', error);
        }
    } else {
        console.log('⚠️ Blacklist webhook URL not configured');
    }
}

// Send log to webhook
async function sendBlacklistLog(type, data) {
    if (!blacklistWebhook) return;

    try {
        const embed = new EmbedBuilder()
            .setTimestamp()
            .setFooter({ text: 'Bot Blacklist System' });

        switch (type) {
            case 'USER_CHECK':
                embed
                    .setTitle('🔍 User Blacklist Check')
                    .setColor(0x3498DB)
                    .addFields(
                        { name: 'User', value: `${data.username} (${data.userId})`, inline: true },
                        { name: 'Guild', value: data.guildName ? `${data.guildName} (${data.guildId})` : 'DM', inline: true },
                        { name: 'Guild Check', value: data.guildCheck, inline: true },
                        { name: 'Global Check', value: data.globalCheck, inline: true }
                    );
                break;

            case 'USER_BLOCKED':
                embed
                    .setTitle('🚫 User Blocked')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'User', value: `${data.username} (${data.userId})`, inline: true },
                        { name: 'Command', value: data.command, inline: true },
                        { name: 'Guild', value: data.guildName ? `${data.guildName} (${data.guildId})` : 'DM', inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'GUILD_JOIN':
                embed
                    .setTitle('🏠 Guild Joined')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Member Count', value: data.memberCount?.toString() || 'Unknown', inline: true },
                        { name: 'Owner', value: data.ownerId ? `<@${data.ownerId}>` : 'Unknown', inline: true }
                    );
                break;

            case 'GUILD_BLACKLISTED_LEAVE':
                embed
                    .setTitle('🚫 Left Blacklisted Guild')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Reason', value: data.reason || 'Guild is blacklisted', inline: true },
                        { name: 'Action', value: 'Auto-left guild', inline: true }
                    );
                break;

            case 'GUILD_FORCE_LEAVE':
                embed
                    .setTitle('🚪 Force Left Guild')
                    .setColor(0x9B59B6)
                    .addFields(
                        { name: 'Guild', value: `${data.guildName} (${data.guildId})`, inline: true },
                        { name: 'Executed By', value: `${data.executedBy} (${data.executedById})`, inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'USER_BLACKLISTED':
                embed
                    .setTitle('🚫 User Added to Blacklist')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Target User', value: `${data.targetUser} (${data.targetUserId})`, inline: true },
                        { name: 'Blacklisted By', value: `${data.blacklistedBy} (${data.blacklistedById})`, inline: true },
                        { name: 'Scope', value: data.scope, inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'USER_UNBLACKLISTED':
                embed
                    .setTitle('✅ User Removed from Blacklist')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Target User', value: `${data.targetUser} (${data.targetUserId})`, inline: true },
                        { name: 'Removed By', value: `${data.removedBy} (${data.removedById})`, inline: true },
                        { name: 'Scope', value: data.scope, inline: true }
                    );
                break;

            case 'GUILD_BLACKLISTED':
                embed
                    .setTitle('🚫 Guild Added to Blacklist')
                    .setColor(0xE74C3C)
                    .addFields(
                        { name: 'Target Guild', value: `${data.guildName || 'Unknown'} (${data.guildId})`, inline: true },
                        { name: 'Blacklisted By', value: `${data.blacklistedBy} (${data.blacklistedById})`, inline: true },
                        { name: 'Auto-Left', value: data.autoLeft ? 'Yes' : 'No', inline: true },
                        { name: 'Reason', value: data.reason, inline: false }
                    );
                break;

            case 'GUILD_UNBLACKLISTED':
                embed
                    .setTitle('✅ Guild Removed from Blacklist')
                    .setColor(0x2ECC71)
                    .addFields(
                        { name: 'Target Guild', value: `${data.guildName || 'Unknown'} (${data.guildId})`, inline: true },
                        { name: 'Removed By', value: `${data.removedBy} (${data.removedById})`, inline: true }
                    );
                break;

            case 'GDPR_BLACKLIST':
                embed
                    .setTitle('🔒 GDPR Blacklist Applied')
                    .setColor(0x800080)
                    .addFields(
                        { name: 'Target User', value: `${data.targetUser} (${data.targetUserId})`, inline: true },
                        { name: 'Applied By', value: `${data.blacklistedBy} (${data.blacklistedById})`, inline: true },
                        { name: 'Type', value: 'GDPR Data Deletion', inline: true },
                        { name: 'Status', value: '🚨 PERMANENT', inline: true },
                        { name: 'Request Details', value: data.requestDetails, inline: false },
                        { name: 'Compliance', value: 'GDPR Data Protection Regulation', inline: true },
                        { name: 'Reversible', value: '❌ NO - Cannot be undone', inline: true }
                    );
                break;

            default:
                embed
                    .setTitle('📝 Blacklist Log')
                    .setColor(0x95A5A6)
                    .setDescription(`Unknown log type: ${type}`)
                    .addFields({ name: 'Data', value: JSON.stringify(data, null, 2).substring(0, 1000), inline: false });
        }

        await blacklistWebhook.send({ embeds: [embed] });
    } catch (error) {
        console.error('❌ Failed to send blacklist log to webhook:', error);
    }
}

module.exports = {
    initializeBlacklistLogger,
    sendBlacklistLog
};
