# 📁 Blacklist System - Code Files Part 3

Final part of the blacklist system code files.

---

## 📄 File 9: `commands/blacklist/blacklist-list.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('blacklist-list')
        .setDescription('View the current blacklist (Developer only)')
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Type of blacklist to view')
                .setRequired(false)
                .addChoices(
                    { name: 'Users', value: 'user' },
                    { name: 'Guilds', value: 'guild' },
                    { name: 'All', value: 'all' }
                ))
        .addBooleanOption(option =>
            option.setName('global')
                .setDescription('View global blacklist')
                .setRequired(false)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const type = interaction.options.getString('type') || 'all';
        const isGlobal = interaction.options.getBoolean('global') || false;
        const guildId = isGlobal ? 'global' : interaction.guild?.id || 'global';

        try {
            const result = await getLocalBlacklist(guildId, type === 'all' ? null : type);

            if (!result.success) {
                return interaction.reply({
                    content: `❌ Error retrieving blacklist: ${result.error}`,
                    flags: 64 // MessageFlags.Ephemeral
                });
            }

            const blacklist = result.blacklist;

            if (blacklist.length === 0) {
                return interaction.reply({
                    content: `📝 No entries found in the ${isGlobal ? 'global' : 'server'} blacklist.`,
                    flags: 64 // MessageFlags.Ephemeral
                });
            }

            const embed = new EmbedBuilder()
                .setTitle(`🚫 Blacklist - ${isGlobal ? 'Global' : 'This Server'}`)
                .setColor(0xFF0000)
                .setFooter({ text: `Total entries: ${blacklist.length}` })
                .setTimestamp();

            let description = '';
            const users = blacklist.filter(entry => entry.type === 'user');
            const guilds = blacklist.filter(entry => entry.type === 'guild');

            if (users.length > 0 && (type === 'all' || type === 'user')) {
                description += '**👤 Blacklisted Users:**\n';
                users.slice(0, 10).forEach((entry, index) => {
                    description += `${index + 1}. <@${entry.userId}> - ${entry.reason}\n`;
                });
                if (users.length > 10) {
                    description += `*...and ${users.length - 10} more users*\n`;
                }
                description += '\n';
            }

            if (guilds.length > 0 && (type === 'all' || type === 'guild')) {
                description += '**🏠 Blacklisted Guilds:**\n';
                guilds.slice(0, 10).forEach((entry, index) => {
                    description += `${index + 1}. ${entry.userId} - ${entry.reason}\n`;
                });
                if (guilds.length > 10) {
                    description += `*...and ${guilds.length - 10} more guilds*\n`;
                }
            }

            embed.setDescription(description || 'No entries found.');

            await interaction.reply({ embeds: [embed], flags: 64 }); // MessageFlags.Ephemeral

        } catch (error) {
            console.error('Error listing blacklist:', error);
            await interaction.reply({
                content: '❌ An error occurred while retrieving the blacklist.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```

---

## 📄 File 10: `commands/blacklist/blacklist-check.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { checkLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('blacklist-check')
        .setDescription('Check if a user or guild is blacklisted (Developer only)')
        .addSubcommand(subcommand =>
            subcommand
                .setName('user')
                .setDescription('Check if a user is blacklisted')
                .addUserOption(option =>
                    option.setName('target')
                        .setDescription('The user to check')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('guild')
                .setDescription('Check if a guild is blacklisted')
                .addStringOption(option =>
                    option.setName('guild_id')
                        .setDescription('The guild ID to check (leave empty for current guild)')
                        .setRequired(false))),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const subcommand = interaction.options.getSubcommand();

        try {
            if (subcommand === 'user') {
                const targetUser = interaction.options.getUser('target');
                
                // Create fake interaction for blacklist check
                const fakeInteraction = {
                    user: targetUser,
                    guild: interaction.guild
                };

                // Check local blacklist
                const localBlacklistCheck = await checkLocalBlacklist(fakeInteraction);

                const embed = new EmbedBuilder()
                    .setTitle('🔍 Blacklist Check - User')
                    .setColor(localBlacklistCheck.isBlacklisted ? 0xFF0000 : 0x00FF00)
                    .addFields(
                        { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: false },
                        { name: 'Local Blacklist', value: localBlacklistCheck.isBlacklisted ? `❌ **Blacklisted**\nReason: ${localBlacklistCheck.reason || 'No reason provided'}` : '✅ Not blacklisted', inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [embed], flags: 64 });

            } else if (subcommand === 'guild') {
                const guildId = interaction.options.getString('guild_id') || interaction.guild?.id;
                
                if (!guildId) {
                    return interaction.reply({
                        content: '❌ Please provide a guild ID or use this command in a server.',
                        flags: 64
                    });
                }

                // Check local blacklist
                const LocalBlacklist = require('../../models/LocalBlacklist');
                const localBlacklist = await LocalBlacklist.findOne({ 
                    userId: guildId,
                    guildId: guildId,
                    type: 'guild'
                });

                const guildName = interaction.client.guilds.cache.get(guildId)?.name || 'Unknown Guild';

                const embed = new EmbedBuilder()
                    .setTitle('🔍 Blacklist Check - Guild')
                    .setColor(localBlacklist ? 0xFF0000 : 0x00FF00)
                    .addFields(
                        { name: 'Guild', value: `${guildName} (${guildId})`, inline: false },
                        { name: 'Local Blacklist', value: localBlacklist ? `❌ **Blacklisted**\nReason: ${localBlacklist.reason}` : '✅ Not blacklisted', inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [embed], flags: 64 });
            }

        } catch (error) {
            console.error('Error checking blacklist:', error);
            await interaction.reply({
                content: '❌ An error occurred while checking the blacklist.',
                flags: 64
            });
        }
    },
};
```

---

## 📄 File 11: `commands/blacklist/force-leave.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { isDeveloper } = require('../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('force-leave')
        .setDescription('Force the bot to leave a specific guild (Developer only)')
        .addStringOption(option =>
            option.setName('guild_id')
                .setDescription('The guild ID to leave')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for leaving (optional)')
                .setRequired(false)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const guildId = interaction.options.getString('guild_id');
        const reason = interaction.options.getString('reason') || 'Forced leave by developer';

        // Prevent leaving current guild without confirmation
        if (interaction.guild && guildId === interaction.guild.id) {
            return interaction.reply({
                content: '❌ Use `/blacklist-guild` instead to blacklist and leave the current guild.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        try {
            const targetGuild = interaction.client.guilds.cache.get(guildId);
            
            if (!targetGuild) {
                return interaction.reply({
                    content: '❌ Bot is not in a guild with that ID.',
                    flags: 64 // MessageFlags.Ephemeral
                });
            }

            // Try to notify the guild before leaving
            try {
                if (targetGuild.systemChannel && targetGuild.systemChannel.permissionsFor(interaction.client.user).has('SendMessages')) {
                    const leaveEmbed = new EmbedBuilder()
                        .setTitle('🚪 Bot Leaving')
                        .setDescription('The bot is leaving this server.')
                        .setColor(0xFFA500)
                        .addFields(
                            { name: 'Reason', value: reason, inline: false },
                            { name: 'Contact', value: 'Contact the bot developers for more information.', inline: false }
                        )
                        .setTimestamp();

                    await targetGuild.systemChannel.send({ embeds: [leaveEmbed] });
                }
            } catch (error) {
                console.error('Failed to send leave notification:', error);
            }

            const guildName = targetGuild.name;
            
            // Log the force leave action
            sendBlacklistLog('GUILD_FORCE_LEAVE', {
                guildName: guildName,
                guildId: guildId,
                executedBy: interaction.user.tag,
                executedById: interaction.user.id,
                reason: reason
            });
            
            // Leave the guild
            await targetGuild.leave();
            
            const embed = new EmbedBuilder()
                .setTitle('✅ Successfully Left Guild')
                .setColor(0x00FF00)
                .addFields(
                    { name: 'Guild', value: `${guildName} (${guildId})`, inline: true },
                    { name: 'Reason', value: reason, inline: true },
                    { name: 'Executed by', value: `${interaction.user}`, inline: true }
                )
                .setTimestamp();

            await interaction.reply({ embeds: [embed] });
            console.log(`🚪 Force left guild: ${guildName} (${guildId}) - Reason: ${reason}`);

        } catch (error) {
            console.error('Error force leaving guild:', error);
            await interaction.reply({
                content: '❌ An error occurred while trying to leave the guild.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```

---

## 📄 File 12: `events/interactionCreate.js`

```javascript
const { checkLocalBlacklist } = require('../middleware/localBlacklist');

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        try {
            // Handle autocomplete interactions FIRST (before blacklist checks)
            if (interaction.isAutocomplete()) {
                // Handle your autocomplete logic here
                return;
            }

            // Check local blacklist for all other interactions
            const localBlacklistCheck = await checkLocalBlacklist(interaction);
            if (localBlacklistCheck.isBlacklisted) {
                return await interaction.reply({
                    content: localBlacklistCheck.message,
                    flags: 64 // MessageFlags.Ephemeral
                });
            }

            // Handle different interaction types
            if (interaction.isChatInputCommand()) {
                // Check if commands are loaded
                if (!client.commands) {
                    console.error('Commands not loaded yet');
                    return await interaction.reply({
                        content: 'Bot is still starting up. Please try again in a moment.',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                }

                const command = client.commands.get(interaction.commandName);
                if (!command) return;

                // Check cooldowns
                if (!client.cooldowns) {
                    client.cooldowns = new Map();
                }
                const { cooldowns } = client;
                if (!cooldowns.has(command.data.name)) {
                    cooldowns.set(command.data.name, new Map());
                }

                const now = Date.now();
                const timestamps = cooldowns.get(command.data.name);
                const cooldownAmount = (command.cooldown || 3) * 1000;

                if (timestamps.has(interaction.user.id)) {
                    const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;

                    if (now < expirationTime) {
                        const timeLeft = (expirationTime - now) / 1000;
                        return await interaction.reply({
                            content: `⏰ Please wait ${timeLeft.toFixed(1)} more seconds before using \`${command.data.name}\` again.`,
                            flags: 64 // MessageFlags.Ephemeral
                        });
                    }
                }

                timestamps.set(interaction.user.id, now);
                setTimeout(() => timestamps.delete(interaction.user.id), cooldownAmount);

                await command.execute(interaction, client);
            }
            // Handle other interaction types as needed...

        } catch (error) {
            console.error(`❌ Error during interaction:`, error);
            const errorMessage = 'There was an error executing this interaction.';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: errorMessage, flags: 64 }); // MessageFlags.Ephemeral
            } else {
                await interaction.reply({ content: errorMessage, flags: 64 }); // MessageFlags.Ephemeral
            }
        }
    }
};
```
