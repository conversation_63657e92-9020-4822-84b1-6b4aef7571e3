const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const RestrictionUserData = require('../../../schemas/restrictionUserData');
const RestrictionExemption = require('../../../schemas/restrictionExemption'); // Import exemption schema
const Settings = require('../../../schemas/settings');
const { v4: uuidv4 } = require('uuid'); // Import UUID for unique codes
const dotenv = require('dotenv');
dotenv.config();

const updateExistingRestrictions = async () => {
    const restrictionsWithoutCode = await RestrictionUserData.find({
        $or: [
            { uniqueCode: { $exists: false } },
            { serverId: { $exists: false } },
            { serverName: { $exists: false } }
        ]
    });
    for (const restriction of restrictionsWithoutCode) {
        if (!restriction.uniqueCode) {
            restriction.uniqueCode = uuidv4(); // Generate a unique code
        }
        if (!restriction.serverId) {
            restriction.serverId = 'UNKNOWN_SERVER_ID'; // Set default value for missing serverId
        }
        if (!restriction.serverName) {
            restriction.serverName = 'Unknown Server'; // Set default value for missing serverName
        }
        await restriction.save();
    }
};

// Ensure existing restrictions are updated when the bot starts
updateExistingRestrictions().catch(console.error);

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: ["ManageGuild", "ManageRoles"],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('restriction')
            .setDescription('Manage restrictions')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('add')
                    .setDescription('Add a restriction')
                    .addUserOption(option => 
                        option.setName('user')
                            .setDescription('The user to restrict')
                            .setRequired(true))
                    .addBooleanOption(option => 
                        option.setName('is_appealable')
                            .setDescription('Is the restriction appealable?')
                            .setRequired(true))
                    .addStringOption(option => 
                        option.setName('reason')
                            .setDescription('The reason for the restriction')
                            .setRequired(false)))
            .addSubcommand(subcommand =>
                subcommand
                    .setName('remove')
                    .setDescription('Remove a restriction')
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user to unrestrict')
                            .setRequired(true))),
    },
};

module.exports.slashRun = async (interaction, client) => {
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    const settings = await Settings.findOne({ guildId: interaction.guild.id }); // Fetch settings here

    if (ownerIds.includes(interaction.user.id)) {
        // Bypass all permission checks for owners
    } else {
        const staffRoleId = settings?.staffRoleId;

        if (!staffRoleId) {
            return interaction.reply({
                content: '❌ The staff role is not set up. Please use `/setstaffrole` to configure it.',
                ephemeral: true,
            });
        }

        if (!interaction.member.roles.cache.has(staffRoleId)) {
            return interaction.reply({
                content: '❌ You do not have the required staff role to use this command.',
                ephemeral: true,
            });
        }
    }

    const subcommand = interaction.options.getSubcommand();
    const user = interaction.options.getUser('user');
    const member = interaction.guild.members.cache.get(user.id);

    // Check if user is GDPR blacklisted (special handling for restrictions)
    let isGDPRUser = false;
    if (subcommand === 'add') {
        const LocalBlacklist = require('../../../models/LocalBlacklist');
        const gdprBlacklist = await LocalBlacklist.findOne({
            userId: user.id,
            guildId: 'global',
            type: 'gdpr'
        });

        if (gdprBlacklist) {
            isGDPRUser = true;
            // GDPR users can be restricted normally, user can choose appeal option
            // Silent handling - no logging needed
        }
    }

    // Fetch settings
    const restrictionRoleId = settings?.restrictionRoleId;
    const logChannelId = settings?.logChannelId;

    if (!restrictionRoleId) {
        return client.errNormal({
            error: 'Restriction role is not configured. Please set it up using the settings command.',
            type: 'reply',
        }, interaction);
    }

    if (subcommand === 'add') {
        // Check if the user is exempted from restrictions
        const isExempted = await RestrictionExemption.findOne({ userId: user.id });
        if (isExempted) {
            return interaction.reply({
                content: `❌ <@${user.id}> is exempted from restrictions and cannot be restricted.`,
                ephemeral: true,
            });
        }

        const isAppealable = interaction.options.getBoolean('is_appealable');
        const reason = interaction.options.getString('reason') || 'No reason provided';

        if (member) {
            const roles = member.roles.cache.filter(role => role.id !== interaction.guild.id);
            const roleIds = roles.map(role => role.id);

            // Check if the user already has an active restriction
            const existingRestriction = await RestrictionUserData.findOne({ userId: user.id, isActive: true });

            if (existingRestriction) {
                return interaction.reply({
                    content: `This user is already restricted. Please remove the existing restriction before adding a new one.`,
                    ephemeral: true,
                });
            }

            // Create a new restriction entry in the database
            if (isGDPRUser) {
                // GDPR users: Only log user ID and minimal data (keep server name for admin purposes)
                await RestrictionUserData.create({
                    userId: user.id,
                    serverId: interaction.guild.id,
                    serverName: interaction.guild.name, // Keep real server name for admin tracking
                    restrictionReason: reason, // Keep real reason for admin tracking
                    isAppealable: isAppealable, // GDPR users CAN appeal
                    isActive: true,
                    roles: [], // No role data stored
                    uniqueCode: uuidv4(),
                    createdAt: new Date(),
                    gdprProtected: true // Flag for GDPR protection
                });

                // Log to GDPR webhook only (no other logging)
                const { sendGDPRLog } = require('../../../utils/gdprLogger');
                sendGDPRLog('GDPR_USER_RESTRICTED', {
                    userId: user.id,
                    restrictedBy: interaction.user.tag,
                    restrictedById: interaction.user.id,
                    serverId: interaction.guild.id,
                    serverName: interaction.guild.name,
                    reason: reason
                });
            } else {
                // Regular users: Full logging
                await RestrictionUserData.create({
                    userId: user.id,
                    serverId: interaction.guild.id,
                    serverName: interaction.guild.name,
                    restrictionReason: reason,
                    isAppealable: isAppealable,
                    isActive: true,
                    roles: roleIds,
                    uniqueCode: uuidv4(),
                    createdAt: new Date(),
                });
            }

            // Check bot permissions before attempting role changes
            const botMember = interaction.guild.members.cache.get(client.user.id);
            if (!botMember.permissions.has('ManageRoles')) {
                return interaction.reply({
                    content: '❌ **Missing Permissions**: I need the "Manage Roles" permission to apply restrictions.',
                    ephemeral: true
                });
            }

            // Check if bot's role is high enough to manage the restriction role
            const restrictionRole = interaction.guild.roles.cache.get(restrictionRoleId);
            if (restrictionRole && botMember.roles.highest.position <= restrictionRole.position) {
                return interaction.reply({
                    content: '❌ **Role Hierarchy Error**: My role must be higher than the restriction role to manage it.',
                    ephemeral: true
                });
            }

            // Remove existing roles with error handling
            for (const role of roles.values()) {
                try {
                    await member.roles.remove(role);
                } catch (error) {
                    // Silent fail - role removal not critical
                }
            }

            // Add restriction role with error handling
            try {
                await member.roles.add(restrictionRoleId);
                // Silent success - no logging needed
            } catch (error) {
                return interaction.reply({
                    content: '❌ **Failed to apply restriction**: Could not add restriction role. Please check bot permissions and role hierarchy.',
                    ephemeral: true
                });
            }
        }

        // Emit custom event to update production stats
        try {
            client.emit('restrictionUpdate', interaction.guild.id);
        } catch (updateError) {
            // Silent fail - not critical
        }

        // Embed response for the command execution
        const embed = client.embed()
            .setTitle('Restriction Penalty')
            .setDescription(`You have successfully restricted the user ${user.tag} from the server`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: '✅ Is Appealable?', value: isAppealable ? 'Yes' : 'No', inline: true },
                { name: '❌ Reason', value: reason, inline: true },
                { name: '🔒 Status', value: 'Active', inline: true },
                { name: 'User ID', value: user.id, inline: true } // Log the user ID
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` });

        await interaction.reply({ embeds: [embed] });

        // Log the restriction action in the log channel (skip for GDPR users)
        if (logChannelId && !isGDPRUser) {
            const logChannel = await client.channels.fetch(logChannelId).catch(() => null);
            if (logChannel) {
                const logEmbed = client.embed()
                    .setTitle('Restriction Added')
                    .setDescription(`A restriction has been added to ${user.tag}`)
                    .setColor('#ff0000')
                    .addFields(
                        { name: 'User', value: `${user.tag} (${user.id})`, inline: true },
                        { name: 'Moderator', value: `${interaction.user.tag} (${interaction.user.id})`, inline: true },
                        { name: 'Is Appealable?', value: isAppealable ? 'Yes' : 'No', inline: true },
                        { name: 'Reason', value: reason, inline: true },
                        { name: 'Status', value: 'Active', inline: true }
                    )
                    .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
                    .setTimestamp();

                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`kick_${user.id}`)
                            .setLabel('Kick User')
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId(`ban_${user.id}`)
                            .setLabel('Ban User')
                            .setStyle(ButtonStyle.Danger)
                    );

                await logChannel.send({ embeds: [logEmbed], components: [actionRow] });
            }
        }
    } else if (subcommand === 'remove') {
        const restrictionData = await RestrictionUserData.findOne({ userId: user.id, isActive: true });

        if (!restrictionData) {
            return interaction.reply({
                content: `This user does not have an active restriction.`,
                ephemeral: true,
            });
        }

        if (member) {
            await member.roles.remove(restrictionRoleId).catch(console.error);

            // Restore previous roles
            if (restrictionData.roles && restrictionData.roles.length > 0) {
                for (const roleId of restrictionData.roles) {
                    const role = interaction.guild.roles.cache.get(roleId);
                    if (role) {
                        await member.roles.add(role).catch(console.error);
                    }
                }
            }
        }

        // Mark restriction as inactive in the database
        restrictionData.isActive = false;
        await restrictionData.save();

        // Emit custom event to update production stats
        client.emit('restrictionUpdate', interaction.guild.id);

        const embed = client.embed()
            .setTitle('Restriction Removed')
            .setDescription(`You have successfully removed the restriction for ${user.tag}`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: '🔒 Status', value: 'Inactive', inline: true }
            );

        await interaction.reply({ embeds: [embed] });

        // Log the removal action in the log channel
        if (logChannelId) {
            const logChannel = await client.channels.fetch(logChannelId).catch(() => null);
            if (logChannel) {
                const logEmbed = client.embed()
                    .setTitle('Restriction Removed')
                    .setDescription('A restriction has been removed.')
                    .setColor('#00ff00')
                    .addFields(
                        { name: 'User Restricted', value: `${user.tag} (${user.id})`, inline: true },
                        { name: 'Staff Who Accepted Appeal', value: `${interaction.user.tag} (${interaction.user.id})`, inline: true },
                        { name: 'Restriction Reason', value: restrictionData.restrictionReason || 'No reason provided', inline: true },
                        { name: 'Status', value: 'Inactive', inline: true },
                        { name: 'Date and Time', value: new Date().toLocaleString(), inline: true }
                    )
                    .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
                    .setTimestamp();
                await logChannel.send({ embeds: [logEmbed] });
            }
        }
    }

    // Handle button interactions for kicking and banning
    client.on('interactionCreate', async (buttonInteraction) => {
        if (!buttonInteraction.isButton()) return;

        const [action, targetUserId] = buttonInteraction.customId.split('_');
        if (action === 'kick' || action === 'ban') {
            const targetMember = buttonInteraction.guild.members.cache.get(targetUserId);
            if (!targetMember) {
                return buttonInteraction.reply({
                    content: '❌ User not found in the server.',
                    ephemeral: true,
                });
            }

            // Prompt staff for a reason
            const modal = new ModalBuilder()
                .setCustomId(`${action}_reason_modal_${targetUserId}`)
                .setTitle(`${action === 'kick' ? 'Kick' : 'Ban'} Reason`);

            const reasonInput = new TextInputBuilder()
                .setCustomId('reason')
                .setLabel(`Why are you ${action === 'kick' ? 'kicking' : 'banning'} this user?`)
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(reasonInput));
            await buttonInteraction.showModal(modal);

            client.once('interactionCreate', async (modalInteraction) => {
                if (!modalInteraction.isModalSubmit() || !modalInteraction.customId.startsWith(`${action}_reason_modal_${targetUserId}`)) return;

                const reason = modalInteraction.fields.getTextInputValue('reason');

                try {
                    if (action === 'kick') {
                        await targetMember.kick(reason);
                        await modalInteraction.reply({
                            content: `✅ Successfully kicked <@${targetUserId}>.`,
                            ephemeral: true,
                        });
                    } else if (action === 'ban') {
                        await targetMember.ban({ reason });
                        await modalInteraction.reply({
                            content: `✅ Successfully banned <@${targetUserId}>.`,
                            ephemeral: true,
                        });
                    }

                    // Log the action in the database
                    await RestrictionUserData.create({
                        userId: targetUserId,
                        serverId: buttonInteraction.guild.id, // Ensure serverId is included
                        serverName: buttonInteraction.guild.name, // Ensure serverName is included
                        restrictionReason: `${action === 'kick' ? 'Kicked' : 'Banned'} by staff: ${reason}`,
                        isAppealable: false,
                        isActive: false,
                        uniqueCode: uuidv4(),
                        createdAt: new Date(),
                    });

                    // Log the action in the log channel
                    const settings = await Settings.findOne({ guildId: buttonInteraction.guild.id });
                    const logChannelId = settings?.logChannelId;

                    if (logChannelId) {
                        const logChannel = await client.channels.fetch(logChannelId).catch(() => null);
                        if (logChannel) {
                            const logEmbed = client.embed()
                                .setTitle(action === 'kick' ? 'User Kicked' : 'User Banned')
                                .setDescription(`A user has been ${action === 'kick' ? 'kicked' : 'banned'} via restriction command.`)
                                .setColor(action === 'kick' ? '#FFA500' : '#FF0000')
                                .addFields(
                                    { name: 'User', value: `<@${targetUserId}> (${targetUserId})`, inline: true },
                                    { name: 'Moderator', value: `${modalInteraction.user.tag} (${modalInteraction.user.id})`, inline: true },
                                    { name: 'Reason', value: reason, inline: true },
                                    { name: 'Action', value: action === 'kick' ? 'Kick' : 'Ban', inline: true }
                                )
                                .setTimestamp();
                            await logChannel.send({ embeds: [logEmbed] });
                        }
                    }
                } catch (error) {
                    console.error(`Error executing ${action} action:`, error);
                    await modalInteraction.reply({
                        content: `❌ Failed to ${action} the user. Please try again later.`,
                        ephemeral: true,
                    });
                }
            });
        }
    });
};
