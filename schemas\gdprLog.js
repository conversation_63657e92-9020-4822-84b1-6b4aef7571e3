const mongoose = require('mongoose');

const gdprLogSchema = new mongoose.Schema({
    user_id: {
        type: String,
        required: true,
        index: true
    },
    reason: {
        type: String,
        required: true,
        default: "GDPR_ERASURE"
    },
    flags: {
        type: [String],
        required: true,
        default: ["NO_DATA_COLLECTION", "BAN_ALL_SERVERS"]
    },
    timestamp: {
        type: String,
        required: true,
        default: () => new Date().toISOString()
    },
    Issuer: {
        type: String,
        required: true
    }
}, {
    collection: 'gdpr_logs',
    timestamps: false // We're using our own timestamp field
});

// Create index for efficient lookups
gdprLogSchema.index({ user_id: 1, timestamp: -1 });
gdprLogSchema.index({ Issuer: 1, timestamp: -1 });

module.exports = mongoose.model('GDPRLog', gdprLogSchema);
