const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, WebhookClient } = require('discord.js');
const generator = require('generate-password');

module.exports.config = {
    enabled: true,
    once: true
};

module.exports.info = {
    name: 'errorCreate'
};

module.exports.eventRun = async (client, err, command, interaction) => {
    if (!client.webhooks || !client.webhooks.errorLogs) {
        client.logs.error("Error: Webhook 'errorLogs' is not defined in client.webhooks");
        return;
    }

    const password = generator.generate({
        length: 10,
        numbers: true
    });

    const errorLog = new WebhookClient({
        id: client.webhooks.errorLogs.id,
        token: client.webhooks.errorLogs.token,
    });

    const embed = client.embed()
        .setTitle(`Code: ${password}`)
        .setDescription(`👤 Name: \`${interaction.user.username}\`\n<:blankEmoji:1348029850539262064><:greenArrow:1324399811553923122> Tag: ${interaction.user.tag}\n<:blankEmoji:1348029850539262064><:greenArrow:1324399811553923122> ID: ${interaction.user.id}\n✅ Guild: ${interaction.guild ? interaction.guild.name : 'DM'}\n<:blankEmoji:1348029850539262064><:greenArrow:1324399811553923122> ID: ${interaction.guild ? interaction.guild.id : 'N/A'}\n💻 Command: ${command}`)
        .addFields(
            { name: '💬 Error', value: `\`\`\`${err}\`\`\``, inline: true },
            { name: '📃 Stack error', value: `\`\`\`${err.stack ? err.stack.substr(0, 1018) : 'No stack trace available'}\`\`\`` }
        )
        .setColor(client.config.discord.defaultColour || 'Red');

    try {
        await errorLog.send({
            username: `SwBase - Notify`,
            embeds: [embed]
        });
    } catch (logError) {
        client.logs.error("Failed to send error log:", logError);
    }

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setLabel("Support server")
                .setURL(client.config.discord.serverInvite)
                .setStyle(ButtonStyle.Link)
        );

    const errEmbed = client.embed()
        .setTitle(`Command Error`)
        .setDescription(`There was an error while executing this command`)
        .addFields(
            { name: `Error Code`, value: `${password}`, inline: true },
            { name: `What Now?`, value: `Consider reporting this error to the developers with the above code`, inline: true }
        )
        .setColor('Red');

    try {
        await interaction.reply({ embeds: [errEmbed], components: [row] });
    } catch (replyError) {
        try {
            await interaction.followUp({ embeds: [errEmbed], components: [row] });
        } catch (followUpError) {
            const failEmbed = client.embed()
                .setTitle(`Failed to Notify User`)
                .setDescription(`Error occurred while attempting to notify the user`)
                .addFields(
                    { name: 'Error Code', value: password, inline: true },
                    { name: 'Original Error', value: `\`\`\`${err}\`\`\``, inline: true },
                    { name: 'Reply Error', value: `\`\`\`${replyError}\`\`\``, inline: true },
                    { name: 'Follow-Up Error', value: `\`\`\`${followUpError}\`\`\`` }
                )
                .setColor('Red');

            try {
                await errorLog.send({ embeds: [failEmbed] });
            } catch (logError) {
                client.logs.error("Failed to send failure log:", logError);
            }
        }
    }
};
