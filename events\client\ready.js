const { ActivityType, EmbedBuilder } = require('discord.js');
const { WebhookClient } = require('discord.js');
const { initializeBlacklistLogger } = require('../../utils/blacklistLogger');
const { initializeGDPRLogger } = require('../../utils/gdprLogger');
require('dotenv').config();

const STARTUP_WEBHOOK_URL = process.env.STARTUP_WEBHOOK; // Webhook URL for startup logs
const startupWebhookClient = STARTUP_WEBHOOK_URL ? new WebhookClient({ url: STARTUP_WEBHOOK_URL }) : null;

module.exports.config = {
    enabled: true,
    once: true
};

module.exports.info = {
    name: 'ready'
};

module.exports.eventRun = async (client) => {
    const isMaintenance = process.env.IS_MAINTENANCE === 'true';
    const isDeveloperMode = process.env.IS_DEVELOPER_MODE === 'true';

    const mode = isMaintenance ? "[MAINTENANCE] " 
          : isDeveloperMode ? "[DEVELOPER] " 
          : "[MAINTENANCE]"; 
// [PRODUCTION]
    client.logs.success(`${mode}Logged on as ${client.user.tag}!`);
    await require("../../utils/database/dbConnector").setupDatabase(client);

    // Initialize blacklist and GDPR loggers
    initializeBlacklistLogger();
    initializeGDPRLogger();

    client.logs.divider();
    
    client.logs.info(`Ready to serve in ${client.guilds.cache.size} servers!`);
    client.logs.divider();
    require('../../utils/metrics/lineCount.js');

    // Log startup message to webhook
    if (startupWebhookClient) {
        const embed = new EmbedBuilder()
            .setTitle('Bot Startup')
            .setDescription('The bot has successfully started.')
            .addFields(
                { name: 'Bot Tag', value: `${client.user.tag}`, inline: true },
                { name: 'Bot ID', value: `${client.user.id}`, inline: true },
                { name: 'Servers', value: `${client.guilds.cache.size}`, inline: true },
                { name: 'Users', value: `${client.users.cache.size}`, inline: true },
                { name: 'Mode', value: mode || 'Development', inline: true }
            )
            .setColor('#5865F2')
            .setTimestamp();

        try {
            await startupWebhookClient.send({ embeds: [embed] });
            client.logs.info('Startup log sent to webhook.');
        } catch (error) {
            client.logs.error('Failed to send startup log to webhook:', error);
        }
    }

    // Set bot presence based on mode
    const presence = isMaintenance
        ? { name: "maintenance mode", type: ActivityType.Playing }
        : isDeveloperMode
        ? { name: "under development", type: ActivityType.Playing }
        : { name: "Restricted Users", type: ActivityType.Watching };

    client.user.setPresence({
        activities: [presence],
        status: 'online'
    });

    // Function to update restricted user statistics
    const updateRestrictedStats = async () => {
        try {
            const guildId = "1264468914386112533";
            const guild = client.guilds.cache.get(guildId);

            if (!guild) return;

            // Get total restricted users from database (all servers combined) for bot status
            const RestrictionUserData = require('../../schemas/restrictionUserData');
            const totalRestricted = await RestrictionUserData.countDocuments({
                isActive: true
            });

            // Get server-specific restricted users for voice channel updates
            const Settings = require('../../schemas/settings');
            const guildSettings = await Settings.findOne({ guildId: guildId });
            const restrictedRoleId = guildSettings?.restrictionRoleId;

            let serverRestrictedCount = 0;
            if (restrictedRoleId) {
                const restrictedRole = guild.roles.cache.get(restrictedRoleId);
                if (restrictedRole) {
                    await guild.members.fetch(); // Ensure all members are cached
                    serverRestrictedCount = restrictedRole.members.size;
                }
            }

            // Update bot status with restricted user count
            let statusMessages = [];

            if (isMaintenance) {
                statusMessages = [
                    { name: "maintenance mode", type: ActivityType.Playing }
                ];
            } else if (isDeveloperMode) {
                statusMessages = [
                    { name: "under development", type: ActivityType.Playing }
                ];
            } else {
                statusMessages = [
                    { name: `${totalRestricted} Restricted Users`, type: ActivityType.Watching },
                    { name: `${guild.memberCount} Total Members`, type: ActivityType.Watching },
                ];
            }

            const randomStatus = statusMessages[Math.floor(Math.random() * statusMessages.length)];

            if (randomStatus.type === ActivityType.Streaming) {
                client.user.setActivity(randomStatus.name, {
                    type: randomStatus.type,
                    url: randomStatus.url
                });
            } else {
                client.user.setPresence({
                    activities: [{ name: randomStatus.name, type: randomStatus.type }],
                    status: 'online'
                });
            }

            // Update voice channels that are set up to show restricted counts (server-specific)
            if (process.env.SHOW_PRODUCTION_STATS === 'true') {
                await updateRestrictedVoiceChannels(guild, serverRestrictedCount);
            }

        } catch (error) {
            client.logs.error('Error updating restricted stats:', error);
        }
    };

    // Function to update voice channels with restricted user counts
    const updateRestrictedVoiceChannels = async (guild, restrictedCount) => {
        try {
            // Look for voice channels with "Restricted:" in their name
            const restrictedChannels = guild.channels.cache.filter(channel =>
                channel.type === 2 && // GUILD_VOICE
                channel.name.toLowerCase().includes('restricted:')
            );

            for (const [, channel] of restrictedChannels) {
                try {
                    const newName = `Restricted: ${restrictedCount}`;
                    if (channel.name !== newName) {
                        await channel.setName(newName);
                        client.logs.info(`Updated voice channel: ${newName}`);
                    }
                } catch (error) {
                    client.logs.error(`Failed to update voice channel ${channel.name}:`, error);
                }
            }
        } catch (error) {
            client.logs.error('Error updating restricted voice channels:', error);
        }
    };

    // Update stats immediately and then every 30 seconds
    updateRestrictedStats();
    setInterval(updateRestrictedStats, 30000);
};
