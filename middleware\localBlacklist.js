const LocalBlacklist = require('../models/LocalBlacklist');
const { sendBlacklistLog } = require('../utils/blacklistLogger');

// Get developer IDs from environment variable
function getDeveloperIds() {
    const devIds = process.env.DEVELOPER_IDS;
    if (!devIds) return [];
    return devIds.split(',').map(id => id.trim());
}

function isDeveloper(userId) {
    const developers = getDeveloperIds();
    return developers.includes(userId);
}

async function checkLocalBlacklist(interaction) {
    try {
        // Skip blacklist check for developers
        if (isDeveloper(interaction.user.id)) {
            return { isBlacklisted: false };
        }

        // Check if user is locally blacklisted in this guild
        let userBlacklist = null;
        let guildCheck = 'NOT FOUND';
        let globalCheck = 'NOT FOUND';

        if (interaction.guild) {
            userBlacklist = await LocalBlacklist.findOne({
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                type: 'user'
            });
            guildCheck = userBlacklist ? 'FOUND' : 'NOT FOUND';
        }

        // If not found in guild-specific, check global blacklist
        if (!userBlacklist) {
            userBlacklist = await LocalBlacklist.findOne({
                userId: interaction.user.id,
                guildId: 'global',
                type: 'user'
            });
            globalCheck = userBlacklist ? 'FOUND' : 'NOT FOUND';
        }

        // Check for GDPR blacklist (highest priority)
        if (!userBlacklist) {
            userBlacklist = await LocalBlacklist.findOne({
                userId: interaction.user.id,
                guildId: 'global',
                type: 'gdpr'
            });
            if (userBlacklist) {
                globalCheck = 'GDPR BLACKLISTED';
            }
        }

        // Log the check
        sendBlacklistLog('USER_CHECK', {
            username: interaction.user.tag,
            userId: interaction.user.id,
            guildName: interaction.guild?.name,
            guildId: interaction.guild?.id,
            guildCheck,
            globalCheck
        });

        if (userBlacklist) {
            console.log(`🚫 BLACKLISTED USER BLOCKED: ${interaction.user.tag} (${interaction.user.id}) - ${userBlacklist.reason}`);

            // Log user blocked
            sendBlacklistLog('USER_BLOCKED', {
                username: interaction.user.tag,
                userId: interaction.user.id,
                command: interaction.commandName || 'Unknown',
                guildName: interaction.guild?.name,
                guildId: interaction.guild?.id,
                reason: userBlacklist.reason,
                type: userBlacklist.type
            });

            const supportServer = process.env.SUPPORT_SERVER_URL || 'https://discord.gg/your-support-server';

            // Special message for GDPR blacklisted users
            if (userBlacklist.type === 'gdpr') {
                return {
                    isBlacklisted: true,
                    type: 'gdpr',
                    reason: userBlacklist.reason,
                    message: `🔒 **GDPR Data Deletion Request Processed**\n\n**Your data has been deleted as requested under GDPR regulations.**\n\nYou have been permanently blocked from using this bot in compliance with your data deletion request. This action cannot be reversed.\n\n📞 **Questions?** Contact the bot developers if you need assistance.`
                };
            }

            // Regular blacklist message
            return {
                isBlacklisted: true,
                type: 'user',
                reason: userBlacklist.reason,
                message: `❌ **You are blacklisted from using this bot.**\n\n**Reason:** ${userBlacklist.reason}\n**Blacklisted by:** <@${userBlacklist.blacklistedBy}>\n\n🔗 **Need help?** Join our support server: ${supportServer}`
            };
        }

        // Check if guild is locally blacklisted (only in guild context)
        if (interaction.guild) {
            const guildBlacklist = await LocalBlacklist.findOne({ 
                userId: interaction.guild.id,
                guildId: interaction.guild.id,
                type: 'guild'
            });
            if (guildBlacklist) {
                console.log(`🚫 BLACKLISTED GUILD BLOCKED: ${interaction.guild.name} (${interaction.guild.id}) - ${guildBlacklist.reason}`);
                const supportServer = process.env.SUPPORT_SERVER_URL || 'https://discord.gg/your-support-server';
                return {
                    isBlacklisted: true,
                    type: 'guild',
                    reason: guildBlacklist.reason,
                    message: `❌ **This server is blacklisted from using this bot.**\n\n**Reason:** ${guildBlacklist.reason}\n**Blacklisted by:** <@${guildBlacklist.blacklistedBy}>\n\n🔗 **Need help?** Join our support server: ${supportServer}`
                };
            }
        }

        // Silent success - no logging needed for non-blacklisted users
        return { isBlacklisted: false };

    } catch (error) {
        console.error('Error checking local blacklist:', error);
        return { isBlacklisted: false }; // Allow command to proceed if check fails
    }
}

async function addToLocalBlacklist(targetId, guildId, reason, blacklistedBy, type = 'user') {
    try {
        const blacklistEntry = new LocalBlacklist({
            userId: targetId,
            guildId: guildId || 'global',
            reason,
            blacklistedBy,
            type
        });

        await blacklistEntry.save();
        return { success: true };
    } catch (error) {
        if (error.code === 11000) {
            return { success: false, error: 'User/Guild is already blacklisted' };
        }
        console.error('Error adding to local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

async function removeFromLocalBlacklist(targetId, guildId, type = 'user') {
    try {
        const result = await LocalBlacklist.deleteOne({
            userId: targetId,
            guildId: guildId || 'global',
            type
        });

        return { success: result.deletedCount > 0 };
    } catch (error) {
        console.error('Error removing from local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

async function getLocalBlacklist(guildId, type = null) {
    try {
        const query = { guildId: guildId || 'global' };
        if (type) query.type = type;

        const blacklist = await LocalBlacklist.find(query).sort({ blacklistedAt: -1 });
        return { success: true, blacklist };
    } catch (error) {
        console.error('Error getting local blacklist:', error);
        return { success: false, error: 'Database error occurred' };
    }
}

module.exports = {
    checkLocalBlacklist,
    addToLocalBlacklist,
    removeFromLocalBlacklist,
    getLocalBlacklist,
    isDeveloper,
    getDeveloperIds
};
