const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const RestrictionExemption = require('../../../schemas/restrictionExemption'); // Import the schema
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // Only owners can use this command
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('preventrestriction')
            .setDescription('Manage users who cannot be restricted')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('add')
                    .setDescription('Add a user to the restriction exemption list')
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user to exempt from restrictions')
                            .setRequired(true)
                    )
            )
            .addSubcommand(subcommand =>
                subcommand
                    .setName('remove')
                    .setDescription('Remove a user from the restriction exemption list')
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user to remove from the exemption list')
                            .setRequired(true)
                    )
            )
            .addSubcommand(subcommand =>
                subcommand
                    .setName('list')
                    .setDescription('View the list of users exempted from restrictions')
            ),
    },
};

module.exports.slashRun = async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    // Check if the user is an owner
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (!ownerIds.includes(interaction.user.id)) {
        return interaction.reply({
            content: 'You do not have permission to use this command.',
            ephemeral: true,
        });
    }

    if (subcommand === 'add') {
        const user = interaction.options.getUser('user');

        const existingExemption = await RestrictionExemption.findOne({ userId: user.id });
        if (existingExemption) {
            return interaction.reply({
                content: `<@${user.id}> is already exempted from restrictions.`,
                ephemeral: true,
            });
        }

        await RestrictionExemption.create({ userId: user.id });

        await interaction.reply({
            content: `✅ Successfully added <@${user.id}> to the restriction exemption list.`,
            ephemeral: true,
        });
    } else if (subcommand === 'remove') {
        const user = interaction.options.getUser('user');

        const existingExemption = await RestrictionExemption.findOne({ userId: user.id });
        if (!existingExemption) {
            return interaction.reply({
                content: `<@${user.id}> is not in the restriction exemption list.`,
                ephemeral: true,
            });
        }

        await RestrictionExemption.deleteOne({ userId: user.id });

        await interaction.reply({
            content: `✅ Successfully removed <@${user.id}> from the restriction exemption list.`,
            ephemeral: true,
        });
    } else if (subcommand === 'list') {
        const exemptions = await RestrictionExemption.find();

        if (exemptions.length === 0) {
            return interaction.reply({
                content: 'No users are currently exempted from restrictions.',
                ephemeral: true,
            });
        }

        const embed = new EmbedBuilder()
            .setTitle('Restriction Exemption List')
            .setDescription('The following users are exempted from restrictions:')
            .addFields(
                exemptions.map(exemption => ({
                    name: 'User',
                    value: `<@${exemption.userId}>`,
                    inline: true,
                }))
            )
            .setColor('#5865F2')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
};
