const { EmbedBuilder, WebhookClient } = require('discord.js');
const LocalBlacklist = require('../../models/LocalBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');
const dotenv = require('dotenv');
dotenv.config();

const guildLogWebhookUrl = process.env.guildLogWebhook;
const guildLogWebhookClient = guildLogWebhookUrl ? new WebhookClient({ url: guildLogWebhookUrl }) : null;

module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'guildCreate'
};

// Note: guild and client are passed
module.exports.eventRun = async (guild, client) => {
    try {
        console.log(`🏠 Joined guild: ${guild.name} (${guild.id}) - Members: ${guild.memberCount}`);

        // Log guild join to blacklist system
        sendBlacklistLog('GUILD_JOIN', {
            guildName: guild.name,
            guildId: guild.id,
            memberCount: guild.memberCount,
            ownerId: guild.ownerId
        });

        // Check if guild is blacklisted
        const guildBlacklist = await LocalBlacklist.findOne({
            userId: guild.id,
            guildId: guild.id,
            type: 'guild'
        });

        if (guildBlacklist) {
            console.log(`🚫 Guild ${guild.name} (${guild.id}) is blacklisted. Auto-leaving...`);

            // Log the auto-leave action
            sendBlacklistLog('GUILD_BLACKLISTED_LEAVE', {
                guildName: guild.name,
                guildId: guild.id,
                reason: guildBlacklist.reason
            });

            // Try to notify the guild before leaving
            try {
                if (guild.systemChannel && guild.systemChannel.permissionsFor(client.user).has('SendMessages')) {
                    const leaveEmbed = new EmbedBuilder()
                        .setTitle('🚫 Bot Removed')
                        .setDescription('This server is blacklisted from using this bot.')
                        .setColor(0xFF0000)
                        .addFields(
                            { name: 'Reason', value: guildBlacklist.reason, inline: false },
                            { name: 'Contact', value: 'Contact the bot developers if you believe this is an error.', inline: false },
                            { name: 'Support Server', value: process.env.SUPPORT_SERVER_URL || 'https://discord.gg/your-support-server', inline: false }
                        )
                        .setTimestamp();

                    await guild.systemChannel.send({ embeds: [leaveEmbed] });
                }
            } catch (error) {
                console.error('Failed to send blacklist notification:', error);
            }

            // Leave the guild
            await guild.leave();
            console.log(`🚫 Successfully left blacklisted guild: ${guild.name} (${guild.id})`);
            return; // Exit early since we left the guild
        }

        // Original guild logging functionality
        if (guildLogWebhookClient) {
            const guildName = guild?.name || 'Unknown';
            const guildId = guild?.id || 'Unknown';
            const memberCount = guild?.memberCount?.toString() || 'Unknown';

            const embed = new EmbedBuilder()
                .setTitle('Bot Invited to a Server')
                .setDescription('The bot has been added to a new server.')
                .addFields(
                    { name: 'Server Name', value: guildName, inline: true },
                    { name: 'Server ID', value: guildId, inline: true },
                    { name: 'Member Count', value: memberCount, inline: true }
                )
                .setColor('#00ff00')
                .setTimestamp();

            await guildLogWebhookClient.send({ embeds: [embed] });
        }

    } catch (error) {
        console.error('Error in guildCreate event:', error);
    }
};
