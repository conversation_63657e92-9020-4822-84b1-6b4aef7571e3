const { SlashCommandBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['ManageGuild'],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('commands')
            .setDescription('View all available commands and their subcommands'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Ensure the interaction is deferred only once
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        // Check if the staff role is set up
//        const staffRole = interaction.guild.roles.cache.find(role => role.name === 'Staff');
//        if (!staffRole) {
//            return await interaction.editReply({
//                content: '❌ The staff role is not set up. Please contact an administrator to configure it.',
//            });
//        }

        const embed = client.embed()
            .setTitle('Available Commands')
            .setDescription('Here is a list of all available commands and their subcommands:')
            .addFields(
                { name: '/about', value: 'Learn more about the bot.', inline: true },
                { name: '/help', value: 'Get help and support for using the bot.', inline: true },
                { name: '/commands', value: 'View all available commands and their subcommands.', inline: true },
                { name: '/restriction', value: 'Manage restrictions for users.\n- **add**: Add a restriction to a user.\n- **remove**: Remove a restriction from a user.', inline: false },
                { name: '/settings', value: 'Manage server settings.\n- **setlogchannel**: Set the logging channel for restrictions.\n- **setappeallogchannel**: Set the logging channel for appeals.\n- **setstaffrole**: Set the staff role.\n- **setrestrictionrole**: Set the restriction role.', inline: false }, 
                { name: '/settings setstaffrole', value: 'Set the staff role for the server.', inline: true },
                { name: '/restrictedhistory', value: 'View the restriction history of a user.', inline: true },
                { name: '/viewhistory', value: 'View the moderation history of a user.', inline: true },
                { name: '/invite', value: 'Get the bot invite link and support server link.', inline: true },
                { name: '/credits', value: 'View the credits for this bot.', inline: true },
                { name: '/changelog', value: 'View the latest updates and changes made to the bot.', inline: true },
                { name: '/developer', value: 'Developer utilities.\n- **args**: Handle developer arguments.', inline: false },
                { name: '/suggestion', value: 'Submit a suggestion for the bot or server.', inline: true },
                { name: '/feedback', value: 'Submit feedback about the bot or server.', inline: true },
                { name: '/announcement', value: 'Post an announcement in all servers with logging channels set up (Owner-only).', inline: false },
                { name: `/bug`, value: `Report a bug or issue with the bot.`, inline: true },
                { name: `/support`, value: `Get support for the bot.`, inline: true },
                { name: `/feedback`, value: `Submit feedback about the bot.`, inline: true },
                { name: `/suggestion`, value: `Submit a suggestion for the bot.`, inline: true },
                { name: `/avatar`, value: `Get the avatar of a user.`, inline: true },
                { name : `/report`, value: `Report a user or a bug(WIP Command).`, inline: true },
                { name: '/stats', value: 'View bot statistics.', inline: true },
                { name: `/ping`, value: `Check the bot\'s ping.`, inline: true },
                { name: `/clear`, value: `Clear messages from a channel.`, inline: true },
            )
            .setColor('#5865F2') 
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` });

        // Edit the deferred reply with the embed
        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('Error executing commands command:', error);

        // Handle errors gracefully
        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    ephemeral: true,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
