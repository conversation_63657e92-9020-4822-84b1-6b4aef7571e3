// Migration script to fix RestrictionUserData documents missing serverId/serverName/uniqueCode
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const RestrictionUserData = require('./schemas/restrictionUserData');

const MONGO_URI = process.env.MONGO_URI || 'YOUR_MONGO_URI_HERE'; // Set your Mongo URI here or use .env

async function migrate() {
  await mongoose.connect(MONGO_URI);
  console.log('Connected to MongoDB');

  const docs = await RestrictionUserData.find({
    $or: [
      { serverId: { $exists: false } },
      { serverName: { $exists: false } },
      { uniqueCode: { $exists: false } }
    ]
  });
  console.log(`Found ${docs.length} documents to update.`);

  for (const doc of docs) {
    if (!doc.serverId) doc.serverId = 'UNKNOWN_SERVER_ID';
    if (!doc.serverName) doc.serverName = 'Unknown Server';
    if (!doc.uniqueCode) doc.uniqueCode = uuidv4();
    await doc.save();
    console.log(`Updated document for userId: ${doc.userId}`);
  }

  console.log('Migration complete.');
  await mongoose.disconnect();
}

migrate().catch(err => {
  console.error('Migration failed:', err);
  process.exit(1);
});
