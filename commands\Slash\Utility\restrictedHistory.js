const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const RestrictionUserData = require('../../../schemas/restrictionUserData');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ["ManageGuild"],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = { 
    Slash: {
        data: new SlashCommandBuilder()
            .setName('restrictedhistory')
            .setDescription('Check if a user has a restriction and its status')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to check restriction history for')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction) => {
    const user = interaction.options.getUser('user');

    try {
        // Defer the interaction to keep it valid while processing
        await interaction.deferReply();

        // Fetch ALL restriction data for the user
        const restrictionRecords = await RestrictionUserData.find({ userId: user.id }).sort({ createdAt: -1 });

        if (!restrictionRecords || restrictionRecords.length === 0) {
            return interaction.editReply({
                content: `No restrictions found for <@${user.id}>.`,
            });
        }

        // Check if any records are GDPR protected
        const hasGDPRRecords = restrictionRecords.some(record => record.gdprProtected);
        const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
        const isOwner = ownerIds.includes(interaction.user.id);

        if (hasGDPRRecords && !isOwner) {
            // Non-owners see limited GDPR view
            const embed = new EmbedBuilder()
                .setTitle('🔒 GDPR Protected User')
                .setDescription('This user is protected under GDPR regulations')
                .setColor('#800080') // Purple for GDPR
                .addFields(
                    { name: '📊 Total Records', value: `${restrictionRecords.length} restriction(s) found`, inline: true },
                    { name: '📋 Information', value: 'User details are protected', inline: true },
                    { name: '⚖️ Compliance', value: 'Data minimization applied', inline: true }
                )
                .setFooter({ text: 'GDPR Data Protection' })
                .setTimestamp();

            return interaction.editReply({ embeds: [embed] });
        }

        // Create embeds for all restrictions (max 10 embeds per message)
        const embeds = [];
        const maxEmbedsPerMessage = 10;
        const recordsToShow = restrictionRecords.slice(0, maxEmbedsPerMessage);

        for (let i = 0; i < recordsToShow.length; i++) {
            const restrictionData = recordsToShow[i];

            // Get server information - use current guild if database doesn't have it
            let serverName = restrictionData.serverName;
            let serverId = restrictionData.serverId;
            let serverInfoNote = '';

            // Check if database has invalid/missing server info
            const hasInvalidServerData = !serverName ||
                serverName === 'Unknown Server' ||
                !serverId ||
                serverId === 'Unknown ID' ||
                serverId === 'UNKNOWN_SERVER_ID';

            if (hasInvalidServerData) {
                if (interaction.guild) {
                    // Use current guild information
                    serverName = interaction.guild.name;
                    serverId = interaction.guild.id;
                    serverInfoNote = ' *(Updated from current server)*';

                    // Update the database record with correct server info
                    try {
                        await RestrictionUserData.findByIdAndUpdate(restrictionData._id, {
                            serverName: interaction.guild.name,
                            serverId: interaction.guild.id
                        });
                    } catch (updateError) {
                        console.error('Failed to update restriction record with server info:', updateError);
                    }
                } else {
                    // Fallback to stored data or unknown
                    serverName = restrictionData.serverName || 'Unknown Server';
                    serverId = restrictionData.serverId || 'Unknown Server ID';
                    serverInfoNote = ' *(Original server unknown)*';
                }
            }

            // Build embed for this restriction
            const embed = new EmbedBuilder()
                .setTitle(`Restriction ${i + 1} of ${restrictionRecords.length}`)
                .setDescription(`Restriction information for <@${user.id}>`)
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setColor(restrictionData.isActive ? '#ff0000' : '#00ff00') // Red for active, green for inactive
                .addFields(
                    { name: 'Status', value: restrictionData.isActive ? 'Active' : 'Inactive', inline: true },
                    { name: 'Reason', value: restrictionData.restrictionReason || 'No reason provided', inline: true },
                    { name: 'Appealable', value: restrictionData.isAppealable ? 'Yes' : 'No', inline: true },
                    { name: 'Server', value: serverName + serverInfoNote, inline: true },
                    { name: 'Server ID', value: serverId, inline: true },
                    { name: 'Date', value: new Date(restrictionData.createdAt).toLocaleString(), inline: true },
                    { name: 'Unique Code', value: restrictionData.uniqueCode || 'No code available', inline: true }
                );

            // Add GDPR owner view details if applicable
            if (restrictionData.gdprProtected && isOwner) {
                embed.addFields(
                    { name: '🔒 GDPR Protected', value: 'Owner view - Full details shown', inline: true }
                );
                embed.setFooter({ text: 'GDPR Data Protection - Owner View' });
            } else {
                embed.setFooter({ text: 'Restriction History' });
            }

            embed.setTimestamp();
            embeds.push(embed);
        }

        // Add summary if there are more records than shown
        if (restrictionRecords.length > maxEmbedsPerMessage) {
            const summaryEmbed = new EmbedBuilder()
                .setTitle('📊 Restriction Summary')
                .setDescription(`Showing ${maxEmbedsPerMessage} of ${restrictionRecords.length} total restrictions`)
                .setColor('#FFA500')
                .addFields(
                    { name: 'Total Records', value: `${restrictionRecords.length}`, inline: true },
                    { name: 'Active Records', value: `${restrictionRecords.filter(r => r.isActive).length}`, inline: true },
                    { name: 'Inactive Records', value: `${restrictionRecords.filter(r => !r.isActive).length}`, inline: true }
                )
                .setFooter({ text: 'Use owner commands to view all records' })
                .setTimestamp();

            embeds.unshift(summaryEmbed); // Add summary at the beginning
        }

        await interaction.editReply({ embeds: embeds });
    } catch (err) {
        console.error('Error fetching restriction history:', err);

        // Reply with an error message
        await interaction.editReply({
            content: 'An error occurred while fetching restriction history. Please try again later.',
        });
    }
};
