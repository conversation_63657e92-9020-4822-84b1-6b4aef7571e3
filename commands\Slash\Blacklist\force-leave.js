const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { isDeveloper } = require('../../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../../utils/blacklistLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('force-leave')
            .setDescription('Force the bot to leave a specific guild (Developer only)')
            .addStringOption(option =>
                option.setName('guild_id')
                    .setDescription('The guild ID to leave')
                    .setRequired(true))
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('Reason for leaving (optional)')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const guildId = interaction.options.getString('guild_id');
    const reason = interaction.options.getString('reason') || 'Forced leave by developer';

    // Prevent leaving current guild without confirmation
    if (interaction.guild && guildId === interaction.guild.id) {
        return interaction.reply({
            content: '❌ Use `/blacklist-guild` instead to blacklist and leave the current guild.',
            ephemeral: true
        });
    }

    try {
        const targetGuild = interaction.client.guilds.cache.get(guildId);
        
        if (!targetGuild) {
            return interaction.reply({
                content: '❌ Bot is not in a guild with that ID.',
                ephemeral: true
            });
        }

        // Try to notify the guild before leaving
        try {
            if (targetGuild.systemChannel && targetGuild.systemChannel.permissionsFor(interaction.client.user).has('SendMessages')) {
                const leaveEmbed = new EmbedBuilder()
                    .setTitle('🚪 Bot Leaving')
                    .setDescription('The bot is leaving this server.')
                    .setColor(0xFFA500)
                    .addFields(
                        { name: 'Reason', value: reason, inline: false },
                        { name: 'Contact', value: 'Contact the bot developers for more information.', inline: false }
                    )
                    .setTimestamp();

                await targetGuild.systemChannel.send({ embeds: [leaveEmbed] });
            }
        } catch (error) {
            console.error('Failed to send leave notification:', error);
        }

        const guildName = targetGuild.name;
        
        // Log the force leave action
        sendBlacklistLog('GUILD_FORCE_LEAVE', {
            guildName: guildName,
            guildId: guildId,
            executedBy: interaction.user.tag,
            executedById: interaction.user.id,
            reason: reason
        });
        
        // Leave the guild
        await targetGuild.leave();
        
        const embed = new EmbedBuilder()
            .setTitle('✅ Successfully Left Guild')
            .setColor(0x00FF00)
            .addFields(
                { name: 'Guild', value: `${guildName} (${guildId})`, inline: true },
                { name: 'Reason', value: reason, inline: true },
                { name: 'Executed by', value: `${interaction.user}`, inline: true }
            )
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
        console.log(`🚪 Force left guild: ${guildName} (${guildId}) - Reason: ${reason}`);

    } catch (error) {
        console.error('Error force leaving guild:', error);
        await interaction.reply({
            content: '❌ An error occurred while trying to leave the guild.',
            ephemeral: true
        });
    }
};
