const { <PERSON>lash<PERSON>ommandBuilder, Embed<PERSON>uilder, MessageFlags } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('changelog')
            .setDescription('View the latest updates and changes made to the bot'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Defer the interaction to keep it valid while processing
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const changelogEntries = [
            { name: 'Update', value: '1.0', inline: false },
            { name: 'Improved Restriction History', value: 'The `/restrictedhistory` command now fetches and displays all restriction entries, including active and inactive ones, directly from the database.' },
            { name: 'Appeal Handling', value: 'When an appeal is accepted, the restriction is marked as inactive in the database immediately, and the history is updated in real-time.' },
            { name: 'Multiple Restrictions', value: 'The `/restriction add` command now creates a new restriction entry for every restriction, preserving all previous restrictions in the database.' },
            { name: 'Owner Commands', value: 'The `/owner wipehistory` command now removes only one restriction entry per execution, and `/owner removehistory` removes a specific restriction by its unique code.' },
            { name: 'Appeals Logging', value: 'Added a dedicated appeals logging channel that can be set using `/settings setappeallogchannel`.' },
            { name: 'Moderation History', value: 'The `/viewhistory` command now displays moderation actions like bans, kicks, and timeouts, along with summaries and recent actions.' },
            { name: 'Updated Buttons', value: 'The "Accept Appeal" button now updates the database and logs the action in real-time.' },
            { name: 'Updated Restriction Schema', value: 'The `RestrictionUserData` schema now includes a unique code for each restriction and an `isActive` field to track the status of restrictions.' },
            { name: 'Added Appeals Logging Channel', value: 'You can now set a separate logging channel for appeals using `/settings setappeallogchannel`.' },
            { name: 'Updated Restriction Logging', value: 'Restrictions added and removed are now logged in the restrictions logging channel.' },
            { name: 'Appeals Logging', value: 'When a user appeals, the appeal is logged in the appeals logging channel.' },
            { name: 'Updated Restriction Removal Logs', value: 'Logs now include the user, staff who accepted the appeal, restriction reason, and date/time.' },
            { name: 'Updated Footer Handling', value: 'All embeds now dynamically read the copyright footer from the `.env` file.' },
            { name: 'Added `/about` Command', value: 'Provides information about the bot and its features.' },
            { name: 'Added `/help` Command', value: 'Offers support resources, including links to the support server and website.' },
            { name: 'Added `/commands` Command', value: 'Displays a list of all available commands in an embed.' },
            { name: 'Added `/changelog` Command', value: 'Displays the latest updates and changes made to the bot.' },
            { name: 'Updated Restriction Command', value: 'Now integrates with the settings command to fetch the restriction role.' },
            { name: 'Updated Settings Command', value: 'Added subcommands to set the logging channel, appeals logging channel, staff role, and restriction role.' },
            { name: 'Updated Restriction Appeal', value: 'Now fetches the logging channel and staff role from the settings schema.' },
            { name: 'Added Blacklist Functionality', value: 'Allows owners to blacklist servers or users from using the bot.' },
            { name: 'Update', value: '1.2', inline: false },
            { name: 'Added `/invite` Command', value: 'Provides the bot invite link and support server link.' },
            { name: 'Improved `/commands` Command', value: 'Now includes all subcommands for better clarity.' },
            { name: 'Added `/suggestion` Command', value: 'Allows users to submit suggestions via a webhook.' },
            { name: 'Added `/announcement` Command', value: 'Allows owners to post announcements in all servers with logging channels set up.' },
            { name: 'Added `/bug` command', value: 'Allows users to report bugs via a webhook.' },
            { name: 'Update', value: '1.1', inline: false },
            { name: 'Removed `/feedback` Command', value: 'Allows users to submit feedback via a webhook.' },
            { name: 'Added `/userreport` Command', value: 'Allows users to report a user that is abusing the bot.' },
            { name: `Added /stats command`, value: 'Displays bot statistics, including server count and user count.' },
            { name: 'Added `/ping` Command', value: 'Displays the bot\'s ping.' },
            { name: 'Added `/clear` Command', value: 'Allows users to clear messages from a channel.' },
        ];

        // Split changelog into chunks of 25 fields
        const chunks = [];
        for (let i = 0; i < changelogEntries.length; i += 25) {
            chunks.push(changelogEntries.slice(i, i + 25));
        }

        // Create and send embeds for each chunk
        for (const [index, chunk] of chunks.entries()) {
            const embed = new EmbedBuilder()
                .setTitle(`Nexoria Development Changelog (Page ${index + 1}/${chunks.length})`)
                .setDescription('Here are the latest updates and changes made to the bot:')
                .addFields(chunk)
                .setColor('#5865F2')
                .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` });

            await interaction.followUp({ embeds: [embed] });
        }
    } catch (error) {
        console.error('Error executing changelog command:', error);
        await interaction.editReply({
            content: 'An error occurred while processing your request. Please try again later.',
            flags: MessageFlags.Ephemeral,
        });
    }
};
