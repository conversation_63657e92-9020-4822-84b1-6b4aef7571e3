const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [], // No specific permissions required
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('ping')
            .setDescription('Check the bot\'s latency'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        const sent = await interaction.reply({ content: 'Pinging...', fetchReply: true });

        const botLatency = sent.createdTimestamp - interaction.createdTimestamp;
        const apiLatency = Math.round(client.ws.ping);

        const embed = new EmbedBuilder()
            .setTitle('🏓 • Pong')
            .setDescription('Check out how fast Nexoria Restrictor is')
            .addFields(
                { name: '🤖 | Nexoria Restrictor latency', value: `${botLatency}ms (${(botLatency / 1000).toFixed(3)}s)`, inline: true },
                { name: '💻 | Discord API Latency', value: `${apiLatency}ms (${(apiLatency / 1000).toFixed(3)}s)`, inline: true },
                { name: '📂 | MongoDB Latency', value: '1ms (0.001s)', inline: true } // Placeholder for MongoDB latency
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setTimestamp();

        await interaction.editReply({ content: null, embeds: [embed] });
    } catch (error) {
        console.error('Error executing ping command:', error);

        if (!interaction.replied && !interaction.deferred) {
            try {
                await interaction.reply({
                    content: 'An error occurred while processing your request. Please try again later.',
                    ephemeral: true,
                });
            } catch (replyError) {
                console.error('Failed to reply to interaction:', replyError);
            }
        }
    }
};
