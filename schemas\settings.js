const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
    guildId: {
        type: String,
        required: true,
        unique: true,
    },
    logChannelId: {
        type: String,
        default: null,
    },
    appealLogChannelId: {
        type: String,
        default: null,
    },
    staffRoleId: {
        type: String,
        default: null, // Staff role ID
    },
    restrictionRoleId: {
        type: String,
        default: null,
    },
    memberRole: {
        type: String,
        default: null,
    },
});

module.exports = mongoose.model('Settings', settingsSchema);
