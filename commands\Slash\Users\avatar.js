const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('avatar')
            .setDescription('View the avatar, banner, and details of a user')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user whose avatar, banner, and details you want to view')
                    .setRequired(false)),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Get the user from the command options or default to the interaction user
        const user = interaction.options.getUser('user') || interaction.user;

        // Fetch the user to get banner and additional information
        const fetchedUser = await client.users.fetch(user.id, { force: true });

        // Get the user's avatar URL
        const avatarURL = fetchedUser.displayAvatarURL({ dynamic: true, size: 1024 });

        // Get the user's banner URL (if available)
        const bannerURL = fetchedUser.bannerURL({ dynamic: true, size: 1024 });

        // Create an embed to display the avatar, banner, and user details
        const embed = new EmbedBuilder()
            .setTitle(`${fetchedUser.tag}'s Profile`)
            .setDescription('Here are the details of the user:')
            .setThumbnail(avatarURL) // Display the avatar as a thumbnail
            .setColor(fetchedUser.accentColor || '#5865F2') // Use the user's accent color if available
            .addFields(
                { name: 'Global Name', value: `${fetchedUser.username}`, inline: true },
                { name: 'Is User a Bot?', value: `${fetchedUser.bot ? 'Yes' : 'No'}`, inline: true },
                { name: 'User ID', value: `${fetchedUser.id}`, inline: false },
                { name: 'User Discriminator', value: `#${fetchedUser.discriminator}`, inline: true },
                { name: 'Accent Color', value: fetchedUser.accentColor ? `#${fetchedUser.accentColor.toString(16)}` : 'None', inline: true },
                { name: 'Mention', value: `<@${fetchedUser.id}>`, inline: false },
                { name: 'Account Created', value: `<t:${Math.floor(fetchedUser.createdTimestamp / 1000)}:F>`, inline: false }
            )
            .setFooter({ text: 'Nexoria Developments Avatar Command' });

        // Add the banner as an image if it exists
        if (bannerURL) {
            embed.setImage(bannerURL); // Display the banner directly in the embed
        }

        // Create buttons for opening the avatar and banner
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Open Avatar')
                    .setStyle(ButtonStyle.Link)
                    .setURL(avatarURL),
                bannerURL
                    ? new ButtonBuilder()
                        .setLabel('Open Banner')
                        .setStyle(ButtonStyle.Link)
                        .setURL(bannerURL)
                    : null
            ).toJSON(); // Ensure the row is valid even if the banner button is not added

        // Reply with the embed and buttons
        await interaction.reply({ embeds: [embed], components: [row] });
    } catch (error) {
        console.error('Error executing avatar command:', error);
        await interaction.reply({
            content: 'An error occurred while processing your request. Please try again later.',
            ephemeral: true,
        });
    }
};
