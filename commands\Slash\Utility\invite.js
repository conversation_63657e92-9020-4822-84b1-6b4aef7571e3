const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('invite')
            .setDescription('Get the bot invite link and support server link'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        const botInvite = process.env.botInvite || 'https://discord.com/oauth2/authorize?client_id=1357901753550508235&permissions=8&scope=bot%20applications.commands';
        const supportServer = process.env.support || 'https://discord.nexoriadevelopment.com';

        const embed = new EmbedBuilder()
            .setTitle('Invite Nexoria Development')
            .setDescription('Use the links below to invite the bot to your server or join the support server.')
            .addFields(
                { name: 'Bot Invite', value: `[Invite the Bot](${botInvite})`, inline: true },
                { name: 'Support Server', value: `[Join Support Server](${supportServer})`, inline: true }
            )
            .setColor('#5865F2')
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Invite Bot')
                    .setStyle(ButtonStyle.Link)
                    .setURL(botInvite),
                new ButtonBuilder()
                    .setLabel('Support Server')
                    .setStyle(ButtonStyle.Link)
                    .setURL(supportServer)
            );

        await interaction.reply({ embeds: [embed], components: [row] });
    } catch (error) {
        console.error('Error executing invite command:', error);
        await interaction.reply({
            content: 'An error occurred while processing your request. Please try again later.',
            ephemeral: true,
        });
    }
};
