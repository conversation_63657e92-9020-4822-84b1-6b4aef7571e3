# 👑 Owner & Blacklist Commands Documentation

## 🔐 **Access Levels**
- **👑 Owner Commands**: Restricted to bot owners only (defined in `process.env.owners`)
- **🛡️ Developer Commands**: Restricted to developers only (defined in `process.env.DEVELOPER_IDS`)

---

## 👑 **OWNER COMMANDS**

### **📢 `/announcement`**
**Purpose**: Send announcements to all servers where the bot is present
**Access**: Owner only
**Features**:
- Posts to logging channels first, fallback to public channels
- Reaches all servers with appropriate permissions
- Detailed delivery statistics

**Options**:
- `title` (required): The announcement title
- `message` (required): The announcement content
- `footer` (optional): Custom footer text

**Example Usage**:
```
/announcement title:"Bot Update" message:"New features available!" footer:"Development Team"
```

**Results Display**:
- ✅ Total Success: How many servers received it
- 📋 Logging Channels: Servers using configured log channels
- 🔄 Fallback Channels: Servers using public channel fallback
- 📈 Success Rate: Percentage of successful deliveries

---

### **🔧 `/owner`**
**Purpose**: Advanced owner-only administrative functions
**Access**: Owner only

#### **Subcommands**:

##### **`/owner removehistory`**
**Purpose**: Remove specific restriction history records
**Options**:
- `code` (required): Unique code of the restriction to remove

**Usage**:
```
/owner removehistory code:abc-123-def-456
```

**Features**:
- Permanently deletes restriction records
- Requires exact unique code for safety
- Confirms deletion success/failure

---

### **🔒 `/gdpr-admin`**
**Purpose**: Comprehensive GDPR system administration
**Access**: Owner only
**Features**: Complete oversight of GDPR blacklisted and restricted users

#### **Subcommands**:

##### **`/gdpr-admin blacklisted-users`**
**Purpose**: View all GDPR blacklisted users
**Options**:
- `limit` (optional): Number of users to show (1-50, default: 10)

**Information Displayed**:
- User ID and mention
- Blacklist date and time
- Who applied the blacklist
- Blacklist reason (truncated)

##### **`/gdpr-admin restricted-users`**
**Purpose**: View all GDPR protected restricted users
**Options**:
- `limit` (optional): Number of restrictions to show (1-50, default: 10)

**Information Displayed**:
- User ID and mention
- Server name where restricted
- Restriction reason
- Active/Inactive status
- Appeal eligibility
- Restriction date
- Unique code for appeals

##### **`/gdpr-admin user-details`**
**Purpose**: Complete GDPR profile for specific user
**Options**:
- `user` (required): User to check GDPR details for

**Comprehensive Information**:
- GDPR blacklist status and details
- All GDPR protected restrictions across servers
- GDPR log entries and audit trail
- Complete timeline of GDPR actions

##### **`/gdpr-admin statistics`**
**Purpose**: System-wide GDPR statistics and metrics

**Statistics Provided**:
- Total GDPR blacklisted users
- Total GDPR protected restrictions
- Active vs inactive restrictions
- Total GDPR log entries
- Recent activity (last 7 days)

---

### **🚫 `/preventrestriction`**
**Purpose**: Manage users who cannot be restricted
**Access**: Owner only

#### **Subcommands**:

##### **`/preventrestriction add`**
**Purpose**: Add user to restriction exemption list
**Options**:
- `user` (required): User to exempt from restrictions

##### **`/preventrestriction remove`**
**Purpose**: Remove user from restriction exemption list
**Options**:
- `user` (required): User to remove from exemption list

---

## 🛡️ **DEVELOPER COMMANDS (Blacklist System)**

### **🚫 `/blacklist-user`**
**Purpose**: Blacklist individual users from bot usage
**Access**: Developer only
**Scope**: Server-specific or global blacklists

**Options**:
- `user` (required): User to blacklist
- `reason` (required): Reason for blacklist
- `scope` (required): "server" or "global"

**Features**:
- Prevents all bot command usage
- Comprehensive webhook logging
- Cannot blacklist other developers
- Confirmation required for safety

---

### **🏠 `/blacklist-guild`**
**Purpose**: Blacklist entire servers
**Access**: Developer only
**Features**: Automatic bot departure from blacklisted servers

**Options**:
- `guild_id` (required): Server ID to blacklist
- `reason` (required): Reason for blacklist

**Automatic Actions**:
- Bot immediately leaves blacklisted server
- Prevents rejoining blacklisted servers
- Comprehensive audit logging

---

### **🔍 `/blacklist-check`**
**Purpose**: Verify blacklist status of users or servers
**Access**: Developer only

**Options**:
- `user` (optional): User to check blacklist status
- `guild_id` (optional): Server ID to check blacklist status

**Information Provided**:
- Current blacklist status
- Blacklist reason and date
- Who applied the blacklist
- Scope (server-specific or global)

---

### **📋 `/blacklist-list`**
**Purpose**: View all blacklisted users and servers
**Access**: Developer only

**Options**:
- `type` (required): "user", "guild", "gdpr", or "all"
- `limit` (optional): Number of entries to show (1-50, default: 10)

**Categories**:
- **Users**: Regular user blacklists
- **Guilds**: Server blacklists
- **GDPR**: GDPR blacklisted users
- **All**: Combined view of all blacklists

---

### **🔓 `/unblacklist-user`**
**Purpose**: Remove users from blacklist
**Access**: Developer only

**Options**:
- `user` (required): User to remove from blacklist
- `scope` (required): "server" or "global"

**Features**:
- Restores bot access for user
- Comprehensive unblacklist logging
- Confirmation of removal success

---

### **🏠 `/unblacklist-guild`**
**Purpose**: Remove servers from blacklist
**Access**: Developer only

**Options**:
- `guild_id` (required): Server ID to remove from blacklist

**Features**:
- Allows bot to rejoin server
- Removes automatic departure enforcement
- Audit trail of unblacklist action

---

### **🚪 `/force-leave`**
**Purpose**: Force bot to leave specific servers
**Access**: Developer only

**Options**:
- `guild_id` (required): Server ID to leave
- `reason` (optional): Reason for leaving

**Features**:
- Immediate server departure
- Does not blacklist the server
- Comprehensive logging of action

---

## 🔒 **GDPR SPECIFIC COMMANDS**

### **🔒 `/blacklist-gdpr`**
**Purpose**: Permanently blacklist users for GDPR data deletion requests
**Access**: Developer only
**Features**: Irreversible GDPR compliance blacklist

**Options**:
- `user` (required): User requesting GDPR deletion
- `request_details` (optional): Details about the GDPR request

**GDPR Compliance Features**:
- **Permanent**: Cannot be reversed or undone
- **Legal Basis**: GDPR Article 17 (Right to Erasure)
- **Confirmation Required**: Must type "CONFIRM GDPR BLACKLIST"
- **User Notification**: Automatic DM about GDPR compliance
- **Audit Logging**: Complete legal audit trail
- **Database Logging**: Structured GDPR log entry

**Legal Implications**:
- User permanently blocked from all bot functionality
- Data collection prohibited
- Complies with data protection regulations
- Cannot be reversed for legal compliance

---

### **ℹ️ `/unblacklist-gdpr`**
**Purpose**: Information about GDPR blacklist removal (cannot actually remove)
**Access**: Developer only
**Features**: Educational command explaining GDPR permanence

**Options**:
- `user` (required): User to check GDPR status

**Information Provided**:
- Current GDPR blacklist status
- Legal explanation of why removal is impossible
- GDPR log information if available
- Compliance requirements and regulations

---

### **📋 `/gdpr-logs`**
**Purpose**: View GDPR audit logs for legal compliance
**Access**: Developer only
**Features**: Complete GDPR audit trail access

**Options**:
- `user` (optional): View logs for specific user
- `limit` (optional): Number of logs to show (1-50, default: 10)

**Audit Information**:
- All GDPR blacklist applications
- User access to GDPR logs (this command)
- GDPR compliance checks
- Complete legal audit trail
- Automatic webhook logging of access

---

## 🔧 **UTILITY COMMANDS**

### **🧪 `/test-blacklist`**
**Purpose**: Test blacklist system functionality
**Access**: Developer only
**Features**: Verify blacklist enforcement is working

**Test Results**:
- Current blacklist status
- System functionality verification
- Error detection and reporting

---

## ⚠️ **IMPORTANT NOTES**

### **🔐 Security Features**:
- **Developer Protection**: Cannot blacklist other developers
- **Self-Protection**: Cannot blacklist yourself
- **Confirmation Required**: Critical actions require confirmation
- **Audit Trails**: All actions logged for accountability

### **🔒 GDPR Compliance**:
- **Permanent Actions**: GDPR blacklists cannot be reversed
- **Legal Basis**: Based on GDPR Article 17 (Right to Erasure)
- **Data Minimization**: GDPR users have minimal data logging
- **Audit Requirements**: Complete legal documentation
- **User Rights**: GDPR users can still appeal restrictions

### **📊 Logging & Monitoring**:
- **Webhook Integration**: All actions logged to dedicated webhooks
- **Database Records**: Permanent audit trail in database
- **Access Monitoring**: Who accessed what and when
- **Error Handling**: Graceful failure with detailed error logs

### **🎯 Best Practices**:
- Always provide clear reasons for blacklist actions
- Use server-specific blacklists when possible
- Review GDPR requests carefully before processing
- Monitor system statistics regularly
- Keep audit trails for legal compliance

## 📝 **GDPR RESTRICTION BEHAVIOR**

### **🔒 GDPR Users and Restrictions**:
When restricting GDPR blacklisted users, the system behaves as follows:

#### **Normal Restriction Process**:
- ✅ **Command Works Normally**: `/restriction add` works exactly the same
- ✅ **Appeal Choice**: Staff can choose whether restriction is appealable or not
- ✅ **Role Management**: Restriction role applied normally
- ✅ **User Experience**: GDPR user gets restricted like any other user

#### **Data Logging Differences**:
- 🔒 **Database**: Stores real server name and reason (for admin tracking)
- 🔒 **Regular Channels**: NO logging to normal log channels
- 🔒 **GDPR Webhook**: Only User ID logged to special GDPR webhook
- 🔒 **History Commands**: Shows "protected under GDPR" to non-owners
- 👑 **Owner View**: Owners can see full details in history commands

#### **Example Restriction**:
```
Staff runs: /restriction add user:@GDPRUser reason:"Spamming" is_appealable:true

✅ Restriction applied normally
🔒 NO regular channel logging
🔒 GDPR webhook gets: User ID 123456789 restricted
👑 Owner can see: Full details in /restrictedhistory
🔒 Regular users see: "Protected under GDPR" message
```

### **🎯 Key Points**:
- **GDPR users are NOT forced to have non-appealable restrictions**
- **Staff chooses appeal option just like with regular users**
- **System maintains GDPR compliance while allowing normal moderation**
- **Owners have full administrative visibility**
- **Regular users see privacy protection messages**

---

## 🔍 **GDPR RESTRICTION LOGGING TECHNICAL DETAILS**

### **📋 How GDPR Detection Works**

When any restriction command is executed, the system automatically checks if the target user is GDPR blacklisted:

```javascript
// Automatic GDPR check during restriction
const LocalBlacklist = require('../../../models/LocalBlacklist');
const gdprBlacklist = await LocalBlacklist.findOne({
    userId: user.id,
    guildId: 'global',
    type: 'gdpr'
});

if (gdprBlacklist) {
    isGDPRUser = true; // Switch to GDPR-compliant logging
}
```

### **💾 Database Storage Differences**

#### **🔒 GDPR User Database Entry:**
```javascript
// GDPR users: Minimal data storage
{
    userId: "123456789012345678",           // ✅ User ID only
    serverId: "987654321098765432",         // ✅ Server ID
    serverName: "My Discord Server",        // ✅ Real server name (admin tracking)
    restrictionReason: "Spamming in chat",  // ✅ Real reason (admin tracking)
    isAppealable: true,                     // ✅ Staff choice (not forced)
    isActive: true,
    roles: [],                              // 🔒 NO role data stored
    uniqueCode: "abc-123-def-456",          // ✅ For appeals
    gdprProtected: true                     // 🔒 GDPR protection flag
}
```

#### **✅ Regular User Database Entry:**
```javascript
// Regular users: Complete data storage
{
    userId: "123456789012345678",           // ✅ User ID
    serverId: "987654321098765432",         // ✅ Server ID
    serverName: "My Discord Server",        // ✅ Server name
    restrictionReason: "Spamming in chat",  // ✅ Restriction reason
    isAppealable: true,                     // ✅ Appeal choice
    isActive: true,
    roles: ["Member", "Verified", "Level5"], // ✅ ALL user roles stored
    uniqueCode: "abc-123-def-456",          // ✅ Unique code
    gdprProtected: false                    // ✅ Not GDPR protected
}
```

### **📊 Logging Behavior Comparison**

| Feature | Regular Users | GDPR Users |
|---------|---------------|------------|
| **Database Storage** | ✅ Complete user data | 🔒 User ID + admin data only |
| **Role Information** | ✅ All roles stored | 🔒 Empty array |
| **Regular Log Channels** | ✅ Full logging with usernames | 🔒 NO logging at all |
| **GDPR Webhook** | ❌ Not used | 🔒 User ID + minimal data only |
| **History Commands** | ✅ Full details shown | 🔒 "Protected under GDPR" |
| **Owner History View** | ✅ Full details | ✅ Full details (admin override) |
| **Appeal Process** | ✅ Normal appeal process | ✅ Normal appeal process |
| **Restriction Function** | ✅ Works normally | ✅ Works normally |

### **🔒 GDPR Webhook Logging Example**

When a GDPR user is restricted, ONLY the GDPR webhook receives this message:

```
🔒 GDPR Protected User Restricted
GDPR COMPLIANCE: Protected User Restriction Applied

🆔 User ID: 123456789012345678
👨‍💼 Restricted By: StaffMember#1234 (ID: 987654321098765432)
📅 Restriction Date: January 15, 2025 at 4:30 PM
🏠 Server: My Discord Server (ID: 987654321098765432)
📋 Reason: Spamming in general chat
🔒 Data Protection: GDPR Compliant - Minimal Data Logged

⚠️ GDPR Notice: This user is GDPR protected. Only User ID and minimal
restriction data is logged for compliance purposes.

📊 Database Entry: Limited data stored:
• User ID only
• Server name (for admin tracking)
• Reason (for admin tracking)
• No personal data retained
• No role information stored
```

### **👁️ Visibility Levels**

#### **🔒 Regular Staff View (GDPR User History):**
```
🔒 GDPR Protected User
This user is protected under GDPR regulations

🔒 Status: Active Restriction
📋 Information: User details are protected
⚖️ Compliance: Data minimization applied
📝 Appealable: Yes - Can appeal
📅 Date: January 15, 2025 at 4:30 PM
🆔 Unique Code: abc-123-def-456
```

#### **👑 Owner View (GDPR User History):**
```
🔒 GDPR Protected User
This user is protected under GDPR regulations

🔒 Status: Active Restriction
📋 Information: Owner view - Full details
⚖️ Compliance: Data minimization applied
📝 Appealable: Yes - Can appeal
📅 Date: January 15, 2025 at 4:30 PM
🆔 Unique Code: abc-123-def-456

🏠 Server (Owner View): My Discord Server
📋 Reason (Owner View): Spamming in general chat
🆔 Server ID (Owner View): 987654321098765432
```

### **⚡ Automatic Process Flow**

1. **Staff runs restriction command** → System checks if user is GDPR blacklisted
2. **If GDPR user detected** → Switch to minimal logging mode
3. **Database storage** → Store User ID + admin data only (no roles)
4. **Channel logging** → Skip all regular log channels completely
5. **GDPR webhook** → Send User ID + minimal audit data only
6. **History commands** → Show "protected under GDPR" to non-owners
7. **Owner commands** → Show full details for administrative purposes

### **🎯 Key Benefits for Team**

- **✅ Normal Workflow**: Staff restricts GDPR users exactly like regular users
- **🔒 Automatic Compliance**: System handles GDPR protection automatically
- **👑 Admin Oversight**: Owners can see full details for server management
- **📋 Legal Compliance**: Complete audit trail for GDPR requirements
- **🛡️ Privacy Protection**: User data minimized while maintaining functionality
- **⚖️ Appeal Process**: GDPR users can still appeal restrictions normally

This documentation provides complete transparency about all administrative commands and their capabilities for proper bot management and legal compliance.
