# 📁 Blacklist System - Code Files Part 4

Final event files for the blacklist system.

---

## 📄 File 13: `events/guildCreate.js`

```javascript
const { EmbedBuilder } = require('discord.js');
const LocalBlacklist = require('../models/LocalBlacklist');
const { sendBlacklistLog } = require('../utils/blacklistLogger');

module.exports = {
    name: 'guildCreate',
    async execute(guild, client) {
        console.log(`Joined guild: ${guild.name} (${guild.id})`);

        // Log guild join
        sendBlacklistLog('GUILD_JOIN', {
            guildName: guild.name,
            guildId: guild.id,
            memberCount: guild.memberCount,
            ownerId: guild.ownerId
        });

        try {
            // Check local blacklist
            const localBlacklist = await LocalBlacklist.findOne({ 
                userId: guild.id,
                guildId: guild.id,
                type: 'guild'
            });

            if (localBlacklist) {
                console.log(`🚫 Guild ${guild.name} (${guild.id}) is blacklisted. Leaving...`);
                
                // Log blacklisted guild leave
                sendBlacklistLog('GUILD_BLACKLISTED_LEAVE', {
                    guildName: guild.name,
                    guildId: guild.id,
                    reason: localBlacklist?.reason || 'Guild is blacklisted'
                });
                
                // Try to send a message to the system channel or owner before leaving
                let notificationSent = false;
                
                // Try system channel first
                if (guild.systemChannel && guild.systemChannel.permissionsFor(client.user).has('SendMessages')) {
                    try {
                        const embed = new EmbedBuilder()
                            .setTitle('🚫 Access Denied')
                            .setDescription('This server is blacklisted from using this bot.')
                            .setColor(0xFF0000)
                            .addFields(
                                { name: 'Reason', value: localBlacklist?.reason || 'Server is on the blacklist', inline: false },
                                { name: 'Contact', value: 'Contact the bot developers if you believe this is an error.', inline: false }
                            )
                            .setTimestamp();

                        await guild.systemChannel.send({ embeds: [embed] });
                        notificationSent = true;
                    } catch (error) {
                        console.error('Failed to send notification to system channel:', error);
                    }
                }

                // Try to DM the owner if system channel failed
                if (!notificationSent && guild.ownerId) {
                    try {
                        const owner = await client.users.fetch(guild.ownerId);
                        const embed = new EmbedBuilder()
                            .setTitle('🚫 Bot Removed from Server')
                            .setDescription(`The bot has been removed from **${guild.name}** because this server is blacklisted.`)
                            .setColor(0xFF0000)
                            .addFields(
                                { name: 'Reason', value: localBlacklist?.reason || 'Server is on the blacklist', inline: false },
                                { name: 'Contact', value: 'Contact the bot developers if you believe this is an error.', inline: false }
                            )
                            .setTimestamp();

                        await owner.send({ embeds: [embed] });
                    } catch (error) {
                        console.error('Failed to send DM to guild owner:', error);
                    }
                }

                // Leave the guild
                await guild.leave();
                console.log(`✅ Successfully left blacklisted guild: ${guild.name} (${guild.id})`);
                return;
            }

            // Guild is not blacklisted, send welcome message if possible
            if (guild.systemChannel && guild.systemChannel.permissionsFor(client.user).has('SendMessages')) {
                try {
                    const embed = new EmbedBuilder()
                        .setTitle('👋 Thanks for adding me!')
                        .setDescription('I\'m ready to help your server!')
                        .setColor(0x00AE86)
                        .addFields(
                            { name: '🎮 Features', value: 'Use `/help` to see all available commands', inline: false },
                            { name: '📝 Getting Started', value: 'Configure me with the setup commands', inline: false }
                        )
                        .setTimestamp();

                    await guild.systemChannel.send({ embeds: [embed] });
                } catch (error) {
                    console.error('Failed to send welcome message:', error);
                }
            }

        } catch (error) {
            console.error('Error in guildCreate event:', error);
        }
    }
};
```

---

## 📄 File 14: `events/messageCreate.js` (Optional - for XP/Message Processing Protection)

```javascript
const { checkLocalBlacklist } = require('../middleware/localBlacklist');

module.exports = {
    name: 'messageCreate',
    async execute(message, client) {
        // Ignore bots and DMs
        if (message.author.bot || !message.guild) return;

        // Check local blacklist for user
        const fakeInteraction = {
            user: message.author,
            guild: message.guild
        };
        const localBlacklistCheck = await checkLocalBlacklist(fakeInteraction);
        if (localBlacklistCheck.isBlacklisted) return;

        // Your message processing logic here (XP gain, etc.)
        // Example:
        // await processXPGain(message);
        // await handleAutomod(message);
    }
};
```

---

## 📄 File 15: Main Bot Integration

Add this to your main bot file (usually `index.js` or `bot.js`):

```javascript
// Add these imports at the top
const { initializeBlacklistLogger } = require('./utils/blacklistLogger');

// In your ready event:
client.once('ready', async () => {
    console.log(`Logged in as ${client.user.tag}`);
    
    // Initialize blacklist logger
    initializeBlacklistLogger();
    
    // Your other initialization code...
});

// Make sure you have cooldowns initialized
client.cooldowns = new Map();
```

---

## 📄 File 16: Environment Variables Template

Create or update your `.env` file:

```env
# Discord Bot Token
DISCORD_TOKEN=your_bot_token_here

# Database Connection (MongoDB)
MONGO_URI=mongodb://localhost:27017/your_database_name

# Blacklist System Configuration
DEVELOPER_IDS=123456789012345678,987654321098765432
SUPPORT_SERVER_URL=https://discord.gg/your-support-server
BLACKLIST_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url_here

# Optional: Admin Bot Integration
MONGODB_URI=************************:port/database?authSource=admin
BOT_NAME=your_bot_name
```

---

## 🚀 Quick Setup Guide

1. **Copy all files** to their respective directories
2. **Install dependencies**: `npm install mongodb discord.js`
3. **Update your `.env`** with the required variables
4. **Initialize in your main bot file** (see File 15)
5. **Deploy commands**: `node deploy-commands.js`
6. **Test the system**: Use `/test-blacklist` command

---

## 📋 Command Summary

| Command | Description | Scope |
|---------|-------------|-------|
| `/blacklist-user` | Add user to blacklist | Server/Global |
| `/unblacklist-user` | Remove user from blacklist | Server/Global |
| `/blacklist-guild` | Add guild to blacklist | Guild-specific |
| `/unblacklist-guild` | Remove guild from blacklist | Guild-specific |
| `/blacklist-list` | View blacklist entries | Server/Global |
| `/blacklist-check` | Check blacklist status | User/Guild |
| `/force-leave` | Force leave a guild | Guild-specific |
| `/test-blacklist` | Test blacklist functionality | N/A |

---

## 🎯 Features Included

- ✅ **Developer-only access** with environment variable configuration
- ✅ **Dual-scope blacklisting** (server-specific and global)
- ✅ **Automatic guild leaving** for blacklisted servers
- ✅ **Comprehensive webhook logging** with rich embeds
- ✅ **Safety measures** (no self-blacklisting, developer protection)
- ✅ **Error handling** and user-friendly messages
- ✅ **Support server integration** in blacklist messages
- ✅ **Database optimization** with proper indexing
- ✅ **Real-time enforcement** on all interactions
- ✅ **Audit trail** for all blacklist actions

**🎉 Your advanced blacklist system is ready to deploy!**

This system provides enterprise-level user management with comprehensive logging and safety features. Perfect for serious Discord bot operations requiring robust blacklist capabilities.
