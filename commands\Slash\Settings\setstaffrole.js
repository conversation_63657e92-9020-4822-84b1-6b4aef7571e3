const { SlashCommandBuilder } = require('discord.js');
const Settings = require('../../../schemas/settings');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['ManageGuild'], // Require Manage Guild permission
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('setstaffrole')
            .setDescription('Set the staff role for the server')
            .addRoleOption(option =>
                option.setName('role')
                    .setDescription('The role to set as the staff role')
                    .setRequired(true)
            ),
    },
};

module.exports.slashRun = async (interaction) => {
    const role = interaction.options.getRole('role');

    try {
        await Settings.findOneAndUpdate(
            { guildId: interaction.guild.id },
            { staffRoleId: role.id },
            { upsert: true, new: true }
        );

        await interaction.reply({
            content: `✅ Successfully set the staff role to <@&${role.id}>.`,
            ephemeral: true,
        });
    } catch (error) {
        console.error('Error setting staff role:', error);
        await interaction.reply({
            content: '❌ Failed to set the staff role. Please try again later.',
            ephemeral: true,
        });
    }
};
