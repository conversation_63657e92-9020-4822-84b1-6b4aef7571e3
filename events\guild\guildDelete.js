const { EmbedBuilder, WebhookClient } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

const guildLogWebhookUrl = process.env.guildLogWebhook;
const guildLogWebhookClient = guildLogWebhookUrl ? new WebhookClient({ url: guildLogWebhookUrl }) : null;

module.exports.config = {
    enabled: true,
    once: false
};

module.exports.info = {
    name: 'guildDelete'
};

module.exports.eventRun = async (guild) => {
    try {
        // Attempt to get details, fallback to 'Unknown'
        const guildName = guild?.name || 'Unknown';
        const guildId = guild?.id || 'Unknown';
        const memberCount = guild?.memberCount?.toString() || 'Unknown';

        console.log(`🏠 Left guild: ${guildName} (${guildId}) - Members: ${memberCount}`);

        if (!guildLogWebhookClient) return;

        const embed = new EmbedBuilder()
            .setTitle('Bot Removed from a Server')
            .setDescription('The bot has been removed from a server.')
            .addFields(
                { name: 'Server Name', value: guildName, inline: true },
                { name: 'Server ID', value: guildId, inline: true },
                { name: 'Member Count (Last Known)', value: memberCount, inline: true }
            )
            .setColor('#ff0000')
            .setTimestamp();

        await guildLogWebhookClient.send({ embeds: [embed] });
    } catch (error) {
        console.error('Error logging guildDelete event:', error);
    }
};
