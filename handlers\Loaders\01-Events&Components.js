const fs = require('fs');
const path = require('path');
require('dotenv').config();

module.exports = async (client) => {
    let totalEventCount = 0;
    let totalComponentCount = 0;

    const loadEvents = () => {
        try {
            const eventDirs = fs.readdirSync('./events');

            eventDirs.forEach(dir => {
                try {
                    const eventFiles = fs.readdirSync(path.join('./events', dir)).filter(file => file.endsWith('.js'));
                    const eventsInDir = eventFiles.length;

                    eventFiles.forEach(file => {
                        try {
                            const event = require(path.resolve('./events', dir, file));

                            if (!event.config || !event.info || !event.eventRun) {
                                client.logs.error(`Invalid structure in event file: ${file}`);
                                return;
                            }

                            if (event.config.enabled) {
                                const {
                                    once
                                } = event.config;
                                const {
                                    name: eventName
                                } = event.info;

                                if (!eventName) {
                                    client.logs.error(`Event file missing 'name': ${file}`);
                                    return;
                                }

                                const execute = (...args) => event.eventRun(...args, client);
                                once ? client.once(eventName, execute) : client.on(eventName, execute);
                            }
                        } catch (error) {
                            client.logs.error(`Error loading event file: ${file}\n${error.message}`);
                        }
                    });

                    totalEventCount += eventsInDir;
                } catch (error) {
                    client.logs.error(`Error reading directory: ${dir}\n${error.message}`);
                }
            });
        } catch (error) {
            console.error(`Critical error reading events folder: ${error.message}`);
        }
    };

    const traverseComponents = (dir, loadComponent) => {
        fs.readdirSync(dir, {
            withFileTypes: true
        }).forEach(entity => {
            const fullPath = path.join(dir, entity.name);
            if (entity.isDirectory()) {
                traverseComponents(fullPath, loadComponent);
            } else if (entity.isFile() && entity.name.endsWith('.js')) {
                loadComponent(fullPath);
            }
        });
    };

    const loadComponent = (filePath) => {
        try {
            const component = require(filePath);
            if (component && typeof component === 'object') {
                const {
                    conf,
                    help
                } = component;
                if (conf && typeof conf === 'object') {
                    if (conf.Button && conf.Button.enabled && client.config.components.button_components) {
                        const buttonIDs = help?.Button?.id;
                        if (buttonIDs) {
                            const ids = Array.isArray(buttonIDs) ? buttonIDs : [buttonIDs];
                            ids.forEach(buttonID => {
                                client.buttonComponents.set(buttonID, component);
                                totalComponentCount++;
                            });
                        } else {
                            client.logs.warn(`Button ID is missing in component: ${filePath}`);
                        }
                    }

                    if (conf.Modal && conf.Modal.enabled && client.config.components.modal_components) {
                        const modalIDs = help?.Modal?.id;
                        if (modalIDs) {
                            const ids = Array.isArray(modalIDs) ? modalIDs : [modalIDs];
                            ids.forEach(modalID => {
                                client.modalComponents.set(modalID, component);
                                totalComponentCount++;
                            });
                        } else {
                            client.logs.warn(`Modal ID is missing in component: ${filePath}`);
                        }
                    }

                    if (conf.SelectMenu && conf.SelectMenu.enabled && client.config.components.select_menu_components) {
                        const selectMenuIDs = help?.SelectMenu?.id;
                        if (selectMenuIDs) {
                            const ids = Array.isArray(selectMenuIDs) ? selectMenuIDs : [selectMenuIDs];
                            ids.forEach(selectMenuID => {
                                client.selectMenuComponents.set(selectMenuID, component);
                                totalComponentCount++;
                            });
                        } else {
                            client.logs.warn(`Select Menu ID is missing in component: ${filePath}`);
                        }
                    }
                } else {
                    client.logs.warn(`Component does not have a valid conf property: ${filePath}`);
                }
            } else {
                client.logs.warn(`Component is not a valid object: ${filePath}`);
            }
        } catch (error) {
            client.logs.error(`Error loading component from ${filePath}: ${error}`);
        }
    };

    client.buttonComponents = new Map();
    client.modalComponents = new Map();
    client.selectMenuComponents = new Map();

    const componentsBaseDir = path.join(__dirname, '../../components');
    const componentTypes = ['buttons', 'modals', 'selectMenus'];

    componentTypes.forEach(type => {
        const typeDir = path.join(componentsBaseDir, type);
        if (fs.existsSync(typeDir) && fs.lstatSync(typeDir).isDirectory()) {
            traverseComponents(typeDir, loadComponent);
        }
    });

    loadEvents();

    client.logs.info(`Events loaded: ${totalEventCount} | Components loaded: ${totalComponentCount === 0 ? '0' : totalComponentCount}`);
};