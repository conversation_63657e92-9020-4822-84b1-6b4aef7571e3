const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { WebhookClient } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

// Initialize webhook
const USERREPORT_WEBHOOK_URL = process.env.USERREPORT_WEBHOOK;
const userReportWebhook = USERREPORT_WEBHOOK_URL ? new WebhookClient({ url: USERREPORT_WEBHOOK_URL }) : null;

module.exports = {
    data: new SlashCommandBuilder()
        .setName('userreport')
        .setDescription('Report a user for misconduct')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user you want to report')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for reporting the user (max 1024 chars)')
                .setRequired(true)
        )
        .addAttachmentOption(option =>
            option.setName('proof')
                .setDescription('Optional screenshot or file as proof')
        ),

    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            if (!userReportWebhook) {
                return interaction.editReply('⚠️ User-report system is not configured.');
            }

            // Gather inputs
            const reportedUser = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason').slice(0, 1024);
            const proof = interaction.options.getAttachment('proof');

            // Build embed
            const embed = new EmbedBuilder()
                .setTitle('🚨 New User Report')
                .addFields(
                    { name: 'Reported User', value: `<@${reportedUser.id}> (${reportedUser.tag})`, inline: true },
                    { name: 'Reported By', value: `<@${interaction.user.id}> (${interaction.user.tag})`, inline: true },
                    { name: 'Reason', value: reason }
                )
                .setColor(0xFF0000)
                .setFooter({ text: `${interaction.guild?.name || 'DMs'} (${interaction.guildId || 'N/A'})` })
                .setTimestamp();

            // Attach proof if present
            if (proof && proof.url?.startsWith('http')) {
                embed.setImage(proof.url);
            }

            // Send to webhook
            await userReportWebhook.send({ embeds: [embed] });
            await interaction.editReply('✅ User report submitted. Thank you!');
        } catch (err) {
            console.error('Error executing userreport command:', err);
            await interaction.editReply('❌ Failed to submit user report.');
        }
    }
};
