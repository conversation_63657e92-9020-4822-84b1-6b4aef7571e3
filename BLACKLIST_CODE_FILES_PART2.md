# 📁 Blacklist System - Code Files Part 2

Continuation of the blacklist system code files.

---

## 📄 File 6: `commands/blacklist/unblacklist-user.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { removeFromLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unblacklist-user')
        .setDescription('Remove a user from the blacklist (Developer only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to unblacklist')
                .setRequired(true))
        .addBooleanOption(option =>
            option.setName('global')
                .setDescription('Remove from global blacklist')
                .setRequired(false)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const targetUser = interaction.options.getUser('user');
        const isGlobal = interaction.options.getBoolean('global') || false;
        const guildId = isGlobal ? 'global' : interaction.guild.id;

        try {
            const result = await removeFromLocalBlacklist(targetUser.id, guildId, 'user');

            if (result.success) {
                // Log the unblacklist action
                sendBlacklistLog('USER_UNBLACKLISTED', {
                    targetUser: targetUser.tag,
                    targetUserId: targetUser.id,
                    removedBy: interaction.user.tag,
                    removedById: interaction.user.id,
                    scope: isGlobal ? 'Global' : 'Server-specific'
                });

                const embed = new EmbedBuilder()
                    .setTitle('✅ User Unblacklisted')
                    .setColor(0x00FF00)
                    .addFields(
                        { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: true },
                        { name: 'Scope', value: isGlobal ? 'Global' : 'This Server', inline: true },
                        { name: 'Unblacklisted by', value: `${interaction.user}`, inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [embed] });
            } else {
                await interaction.reply({
                    content: '❌ User was not found in the blacklist.',
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } catch (error) {
            console.error('Error unblacklisting user:', error);
            await interaction.reply({
                content: '❌ An error occurred while unblacklisting the user.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```

---

## 📄 File 7: `commands/blacklist/blacklist-guild.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { addToLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('blacklist-guild')
        .setDescription('Blacklist a guild from using the bot (Developer only)')
        .addStringOption(option =>
            option.setName('guild_id')
                .setDescription('The guild ID to blacklist')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for blacklisting')
                .setRequired(true)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const guildId = interaction.options.getString('guild_id');
        const reason = interaction.options.getString('reason');

        // Prevent blacklisting current guild if command is run in a guild
        if (interaction.guild && guildId === interaction.guild.id) {
            return interaction.reply({
                content: '❌ You cannot blacklist the current guild.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        try {
            const result = await addToLocalBlacklist(
                guildId,
                guildId,
                reason,
                interaction.user.id,
                'guild'
            );

            if (result.success) {
                // Check if bot is currently in the blacklisted guild
                const targetGuild = interaction.client.guilds.cache.get(guildId);
                
                // Log the guild blacklist action
                sendBlacklistLog('GUILD_BLACKLISTED', {
                    guildName: targetGuild?.name || 'Unknown',
                    guildId: guildId,
                    blacklistedBy: interaction.user.tag,
                    blacklistedById: interaction.user.id,
                    reason: reason,
                    autoLeft: !!targetGuild
                });

                const embed = new EmbedBuilder()
                    .setTitle('✅ Guild Blacklisted')
                    .setColor(0xFF0000)
                    .addFields(
                        { name: 'Guild ID', value: guildId, inline: true },
                        { name: 'Reason', value: reason, inline: true },
                        { name: 'Blacklisted by', value: `${interaction.user}`, inline: true }
                    )
                    .setTimestamp();

                if (targetGuild) {
                    embed.addFields({ name: 'Action', value: '🚪 Bot will leave the guild immediately', inline: false });
                    
                    await interaction.reply({ embeds: [embed] });
                    
                    // Try to notify the guild before leaving
                    try {
                        if (targetGuild.systemChannel && targetGuild.systemChannel.permissionsFor(interaction.client.user).has('SendMessages')) {
                            const leaveEmbed = new EmbedBuilder()
                                .setTitle('🚫 Bot Removed')
                                .setDescription('This server has been blacklisted from using this bot.')
                                .setColor(0xFF0000)
                                .addFields(
                                    { name: 'Reason', value: reason, inline: false },
                                    { name: 'Contact', value: 'Contact the bot developers if you believe this is an error.', inline: false }
                                )
                                .setTimestamp();

                            await targetGuild.systemChannel.send({ embeds: [leaveEmbed] });
                        }
                    } catch (error) {
                        console.error('Failed to send leave notification:', error);
                    }
                    
                    // Leave the guild
                    await targetGuild.leave();
                    console.log(`🚫 Left blacklisted guild: ${targetGuild.name} (${guildId})`);
                } else {
                    await interaction.reply({ embeds: [embed] });
                }
            } else {
                await interaction.reply({
                    content: `❌ Failed to blacklist guild: ${result.error}`,
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } catch (error) {
            console.error('Error blacklisting guild:', error);
            await interaction.reply({
                content: '❌ An error occurred while blacklisting the guild.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```

---

## 📄 File 8: `commands/blacklist/unblacklist-guild.js`

```javascript
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { removeFromLocalBlacklist, isDeveloper } = require('../../middleware/localBlacklist');
const { sendBlacklistLog } = require('../../utils/blacklistLogger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unblacklist-guild')
        .setDescription('Remove a guild from the blacklist (Developer only)')
        .addStringOption(option =>
            option.setName('guild_id')
                .setDescription('The guild ID to unblacklist')
                .setRequired(true)),
    async execute(interaction) {
        // Check if user is a developer
        if (!isDeveloper(interaction.user.id)) {
            return interaction.reply({
                content: '❌ This command is restricted to developers only.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }

        const guildId = interaction.options.getString('guild_id');

        try {
            const result = await removeFromLocalBlacklist(guildId, guildId, 'guild');

            if (result.success) {
                const targetGuild = interaction.client.guilds.cache.get(guildId);
                const guildName = targetGuild?.name || 'Unknown Guild';

                // Log the unblacklist action
                sendBlacklistLog('GUILD_UNBLACKLISTED', {
                    guildName: guildName,
                    guildId: guildId,
                    removedBy: interaction.user.tag,
                    removedById: interaction.user.id
                });

                const embed = new EmbedBuilder()
                    .setTitle('✅ Guild Unblacklisted')
                    .setColor(0x00FF00)
                    .addFields(
                        { name: 'Guild', value: `${guildName} (${guildId})`, inline: true },
                        { name: 'Unblacklisted by', value: `${interaction.user}`, inline: true },
                        { name: 'Status', value: targetGuild ? 'Bot is currently in this guild' : 'Bot is not in this guild', inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [embed] });
            } else {
                await interaction.reply({
                    content: '❌ Guild was not found in the blacklist.',
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } catch (error) {
            console.error('Error unblacklisting guild:', error);
            await interaction.reply({
                content: '❌ An error occurred while unblacklisting the guild.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },
};
```
