# Production Statistics Feature

This document explains the new production statistics feature that shows how many users are restricted in your Discord server.

## Environment Configuration

### New Environment Variable

Add this to your `.env` file:

```env
SHOW_PRODUCTION_STATS=true
```

### Existing Variables

The feature respects these existing environment variables:

```env
IS_MAINTENANCE=false
IS_DEVELOPER_MODE=true
```

## Features

### 1. Bot Status Updates

The bot's status will automatically show restricted user counts:

- **Production Mode**: Shows "X Restricted Users" or "X Total Members"
- **Developer Mode**: Shows "under development"
- **Maintenance Mode**: Shows "maintenance mode"

### 2. Voice Channel Auto-Updates

Voice channels with "Restricted:" in their name will automatically update to show current counts:

- Updates in real-time when restrictions are added/removed
- Updates every 30 seconds as a backup
- Format: `Restricted: X` (where X is the current count)

### 3. Production Statistics Command

Use `/productionstats` to view comprehensive server statistics:

- Total active restrictions
- Total all-time restrictions
- Restriction rate (percentage of members)
- Appealable vs non-appealable restrictions
- Recent restrictions (last 7 days)
- Server member count
- Restriction role information
- Settings configuration status

### 4. Production Channel Setup

Use `/setupproductionchannel` to configure a voice channel for automatic updates:

- Select any voice channel
- Channel name will be updated to show current restricted count
- Automatic updates when restrictions change
- Real-time synchronization

## Commands

### `/productionstats`
- **Permission Required**: Administrator
- **Description**: Display comprehensive production statistics
- **Cooldown**: 5 seconds (3 seconds for premium)

### `/setupproductionchannel <channel>`
- **Permission Required**: Administrator
- **Bot Permission Required**: Manage Channels
- **Description**: Set up a voice channel for automatic restricted user count display
- **Parameters**:
  - `channel`: Voice channel to configure

### `/setuprestrictedvc <channel>` (Enhanced)
- **Permission Required**: Administrator
- **Description**: Manual setup for restricted user count display
- **Parameters**:
  - `channel`: Voice channel to update

## How It Works

### Automatic Updates

The system automatically updates statistics when:

1. **Restrictions Added**: Via `/restriction add` command
2. **Restrictions Removed**: Via `/restriction remove` command
3. **Appeals Accepted**: When staff accept restriction appeals
4. **Appeals Declined**: When staff decline restriction appeals
5. **Periodic Updates**: Every 30 seconds for voice channels

### Event System

The bot uses a custom event system:

- `restrictionUpdate` event is emitted when restrictions change
- Event handlers update voice channels and bot status
- Real-time synchronization across all features

### Database Integration

Statistics are pulled from the `RestrictionUserData` collection:

- Filters by `serverId` for guild-specific counts
- Filters by `isActive: true` for current restrictions
- Supports historical data analysis

## Setup Instructions

### 1. Enable Production Stats

Add to your `.env` file:
```env
SHOW_PRODUCTION_STATS=true
```

### 2. Restart the Bot

Restart your bot to load the new environment variable.

### 3. Set Up Voice Channel (Optional)

Run the command:
```
/setupproductionchannel channel:#your-voice-channel
```

### 4. View Statistics

Run the command:
```
/productionstats
```

## Troubleshooting

### Voice Channel Not Updating

1. Check bot permissions: Ensure the bot has "Manage Channels" permission
2. Verify environment variable: `SHOW_PRODUCTION_STATS=true`
3. Check channel name: Must contain "Restricted:" (case-insensitive)
4. Restart the bot if needed

### Statistics Not Showing

1. Verify administrator permissions for commands
2. Check if `SHOW_PRODUCTION_STATS=true` in environment
3. Ensure database connection is working
4. Check console logs for errors

### Bot Status Not Updating

1. Verify the bot is not in maintenance mode (`IS_MAINTENANCE=false`)
2. Check if developer mode is affecting status (`IS_DEVELOPER_MODE`)
3. Ensure the target guild ID matches your server

## Security Considerations

- Statistics commands require Administrator permission
- Production stats can be disabled via environment variable
- Voice channel updates respect Discord rate limits
- Database queries are optimized for performance

## Performance Notes

- Voice channel updates are rate-limited to prevent API abuse
- Statistics are cached and updated efficiently
- Database queries use indexes for optimal performance
- Updates only occur when necessary (name changes)

## Support

If you encounter issues with the production statistics feature:

1. Check the console logs for error messages
2. Verify all environment variables are set correctly
3. Ensure proper permissions are configured
4. Contact support if problems persist
