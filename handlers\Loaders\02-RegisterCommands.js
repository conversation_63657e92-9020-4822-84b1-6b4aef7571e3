const fs = require("fs");
const path = require("path");
const https = require("https");
const { Collection } = require("discord.js");

function getFilesRecursively(dir) {
  const files = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      files.push(...getFilesRecursively(fullPath));
    } else if (entry.isFile() && entry.name.endsWith(".js")) {
      files.push(fullPath);
    }
  }

  return files;
}

module.exports = async function RegisterCommands(client) {
  const commandsBasePath = path.join(__dirname, "../../commands");
  if (!fs.existsSync(commandsBasePath)) return client.logs.warn("No 'commands' folder found. Skipping...");

  client.application_commands = new Collection();
  client.contextMenu_commands = new Collection();
  client.prefix_commands = new Collection();

  const slashCommands = [];
  const contextMenuCommands = [];
  const prefixCommands = [];

  const commandTypes = ["Slash", "ContextMenu", "Prefix"];

  for (const commandType of commandTypes) {
    const typePath = path.join(commandsBasePath, commandType);
    if (!fs.existsSync(typePath)) continue;

    const commandFiles = getFilesRecursively(typePath);

    for (const filePath of commandFiles) {
      try {
        const commandPath = require.resolve(filePath);
        if (require.cache[commandPath]) {
          delete require.cache[commandPath];
        }

        const command = require(filePath);
        if (!command.help || !command.conf) continue;

        if (commandType === "Slash" && command.conf.Slash.enabled) {
          const commandData = command.help.Slash.data.toJSON();
          console.log('Registering command:', JSON.stringify(commandData, null, 2)); // Debug log
          client.application_commands.set(command.help.Slash.data.name, command);
          slashCommands.push(commandData);
        }

        if (commandType === "ContextMenu" && command.conf.ContextMenu.enabled) {
          client.contextMenu_commands.set(command.help.ContextMenu.data.name, command);
          contextMenuCommands.push(command.help.ContextMenu.data.toJSON());
        }

        if (commandType === "Prefix" && command.conf.Prefix.enabled) {
          client.prefix_commands.set(command.help.Prefix.name, command);
          prefixCommands.push(command.help.Prefix.name);
        }
      } catch (error) {
        if (error.status === 401) {
          client.logs.error("Command registration failed: Authentication error - verify your token permissions");
        } else if (error.message && error.message.includes("snowflake")) {
          client.logs.error(`Command registration failed: Invalid bot ID (${client.config.client.app_id})`);
        } else {
          client.logs.error(`API Error: ${error.message || error}`);
        }
      }
    }
  }

  const route = `https://discord.com/api/v10/applications/${client.config.client.app_id}/commands`;

  const request = (method, url, body) =>
    new Promise((resolve, reject) => {
      const req = https.request(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bot ${client.config.client.token}`,
        },
      });

      req.on("error", reject);
      req.on("response", (res) => {
        const data = [];
        res.on("data", (chunk) => data.push(chunk));
        res.on("end", () => {
          try {
            const response = JSON.parse(data.join(""));
            if (res.statusCode >= 200 && res.statusCode < 300) {
              return resolve(response);
            }
            console.error(`[Discord API Error] Status: ${res.statusCode}, Response:`, response);
            reject(new Error(`[Discord API: ${res.statusCode}] ${response.message}`));
          } catch (error) {
            reject(error);
          }
        });
      });

      if (body) req.write(JSON.stringify(body));
      req.end();
    });

  try {
    client.logs.info("Registering application commands...");
    await request("PUT", route, [...slashCommands, ...contextMenuCommands]); // Force re-registration
    client.logs.command(`Command Deployment:
  - Slash Commands: ${slashCommands.length}
  - Context Menu Commands: ${contextMenuCommands.length}
  - Prefix Commands: ${prefixCommands.length}`);
  } catch (error) {
    client.logs.error(error);
  }
};
