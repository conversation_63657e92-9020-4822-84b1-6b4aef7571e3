const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { isDeveloper } = require('../../../middleware/localBlacklist');
const { sendGDPRLog } = require('../../../utils/gdprLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('unblacklist-gdpr')
            .setDescription('Information about GDPR blacklist removal (Developer only)')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('The user to check GDPR blacklist status for')
                    .setRequired(true)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const targetUser = interaction.options.getUser('user');

    // Check if user is GDPR blacklisted
    const LocalBlacklist = require('../../../models/LocalBlacklist');
    const gdprBlacklist = await LocalBlacklist.findOne({
        userId: targetUser.id,
        guildId: 'global',
        type: 'gdpr'
    });

    const embed = new EmbedBuilder()
        .setTitle('🔒 GDPR Blacklist Information')
        .setColor(gdprBlacklist ? 0xFF0000 : 0x00FF00)
        .addFields(
            { name: 'User', value: `${targetUser} (${targetUser.id})`, inline: false }
        )
        .setTimestamp();

    if (gdprBlacklist) {
        // Get GDPR log information
        const GDPRLog = require('../../../schemas/gdprLog');
        const gdprLog = await GDPRLog.findOne({ user_id: targetUser.id });

        embed
            .setDescription('**This user is GDPR blacklisted and CANNOT be removed**')
            .addFields(
                { name: '🚫 Status', value: 'GDPR Blacklisted', inline: true },
                { name: '📅 Blacklisted Date', value: `<t:${Math.floor(gdprBlacklist.blacklistedAt.getTime() / 1000)}:F>`, inline: true },
                { name: '👤 Blacklisted By', value: `<@${gdprBlacklist.blacklistedBy}>`, inline: true },
                { name: '📋 Reason', value: gdprBlacklist.reason, inline: false }
            );

        // Add GDPR log information if available
        if (gdprLog) {
            embed.addFields(
                { name: '🔒 GDPR Log Entry', value: `**Reason:** ${gdprLog.reason}\n**Flags:** ${gdprLog.flags.join(', ')}\n**Issuer:** <@${gdprLog.Issuer}>`, inline: false }
            );
        }

        embed.addFields(
            { name: '⚠️ GDPR Compliance', value: 'This blacklist was applied due to a GDPR data deletion request', inline: false },
            { name: '🚨 PERMANENT', value: '**GDPR blacklists CANNOT be removed or reversed**\n\nThis is to ensure compliance with data protection laws and the user\'s explicit request for data deletion.', inline: false },
            { name: '📞 Legal Requirements', value: 'Removing a GDPR blacklist would violate:\n• User\'s data deletion request\n• GDPR compliance requirements\n• Data protection regulations', inline: false },
            { name: '🔒 Final Status', value: '**This user will remain permanently blocked from all bot functionality**', inline: false }
        )
        .setFooter({ text: 'GDPR Blacklist - Cannot Be Removed' });
    } else {
        embed
            .setDescription('This user is not GDPR blacklisted')
            .addFields(
                { name: '✅ Status', value: 'Not GDPR Blacklisted', inline: true },
                { name: '📋 Information', value: 'This user has not requested GDPR data deletion', inline: false },
                { name: '⚠️ About GDPR Blacklists', value: 'GDPR blacklists are permanent and applied when users request data deletion under GDPR regulations. They cannot be removed once applied.', inline: false }
            )
            .setFooter({ text: 'GDPR Status Check' });
    }

    // Log GDPR compliance check for legal audit
    sendGDPRLog('GDPR_COMPLIANCE_CHECK', {
        targetUser: targetUser.tag,
        targetUserId: targetUser.id,
        checkedBy: interaction.user.tag,
        checkedById: interaction.user.id,
        gdprStatus: gdprBlacklist ? '🔒 GDPR Blacklisted (PERMANENT)' : '✅ Not GDPR Blacklisted',
        complianceStatus: gdprBlacklist ? '✅ COMPLIANT - Cannot be removed' : '✅ COMPLIANT - No restrictions',
        checkReason: 'GDPR status verification'
    });

    await interaction.reply({ embeds: [embed], ephemeral: true });
};
