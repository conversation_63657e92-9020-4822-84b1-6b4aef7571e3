const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, WebhookClient } = require('discord.js');
const RestrictionUserData = require('../../../schemas/restrictionUserData'); // Import RestrictionUserData schema
const dotenv = require('dotenv');
dotenv.config();

const ownerAddWebhookUrl = process.env.owneradd;
const ownerAddWebhookClient = ownerAddWebhookUrl ? new WebhookClient({ url: ownerAddWebhookUrl }) : null;

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('owner')
            .setDescription('Owner-only commands')
            .addSubcommand(subcommand =>
                subcommand
                    .setName('addowner')
                    .setDescription('Add a new owner')
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user to add as an owner')
                            .setRequired(true)
                    )
            )
            .addSubcommand(subcommand =>
                subcommand
                    .setName('wipehistory')
                    .setDescription("Wipe a user's restriction history")
                    .addUserOption(option =>
                        option.setName('user')
                            .setDescription('The user whose restriction history to wipe')
                            .setRequired(true)
                    )
            )
            .addSubcommand(subcommand =>
                subcommand
                    .setName('removehistory')
                    .setDescription('Remove a specific restriction history entry by unique code')
                    .addStringOption(option =>
                        option.setName('code')
                            .setDescription('The unique code of the restriction history entry to remove')
                            .setRequired(true)
                    )
            ),
    },
};

module.exports.slashRun = async (interaction, client) => {
    const subcommand = interaction.options.getSubcommand();

    // Read owner IDs from the environment variable
    const ownerIds = process.env.owners ? process.env.owners.split(',') : [];
    if (!ownerIds.includes(interaction.user.id)) {
        return interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
    }

    if (subcommand === 'addowner') {
        const user = interaction.options.getUser('user');

        // Add the new owner to the environment variable
        const updatedOwners = [...ownerIds, user.id];
        process.env.owners = updatedOwners.join(',');

        // Log the addition of the new owner
        if (ownerAddWebhookClient) {
            const embed = new EmbedBuilder()
                .setTitle('New Owner Added')
                .setDescription('A new owner has been added to the bot.')
                .addFields(
                    { name: 'New Owner', value: `<@${user.id}> (${user.tag})`, inline: true },
                    { name: 'Added By', value: `<@${interaction.user.id}> (${interaction.user.tag})`, inline: true }
                )
                .setColor('#00ff00')
                .setTimestamp();

            await ownerAddWebhookClient.send({ embeds: [embed] });
        }

        interaction.reply({
            content: `Successfully added <@${user.id}> as a new owner. They now have access to all commands.`,
            ephemeral: true,
        });
    } else if (subcommand === 'wipehistory') {
        const user = interaction.options.getUser('user');

        try {
            // Delete ALL restriction entries for the user (global wipe)
            const result = await RestrictionUserData.deleteMany({ userId: user.id });

            if (result.deletedCount > 0) {
                interaction.reply({
                    content: `Successfully wiped all restriction history for <@${user.id}> (${result.deletedCount} record(s) deleted).`,
                    ephemeral: true,
                });
            } else {
                interaction.reply({
                    content: `No restriction history found for <@${user.id}>.`,
                    ephemeral: true,
                });
            }
        } catch (err) {
            console.error('Error wiping restriction history:', err);
            interaction.reply({
                content: 'Failed to wipe restriction history. Please try again later.',
                ephemeral: true,
            });
        }
    } else if (subcommand === 'removehistory') {
        const uniqueCode = interaction.options.getString('code');

        try {
            // Find the restriction first to get details
            const restriction = await RestrictionUserData.findOne({ uniqueCode });

            if (!restriction) {
                return interaction.reply({
                    content: `No restriction history found with unique code \`${uniqueCode}\`.`,
                    ephemeral: true,
                });
            }

            // Delete the restriction (global search, no server filtering)
            const result = await RestrictionUserData.deleteOne({ uniqueCode });

            if (result.deletedCount > 0) {
                interaction.reply({
                    content: `Successfully removed restriction history with unique code \`${uniqueCode}\`.\n**User:** <@${restriction.userId}>\n**Server:** ${restriction.serverName || 'Unknown'}\n**Reason:** ${restriction.restrictionReason || 'No reason'}`,
                    ephemeral: true,
                });
            } else {
                interaction.reply({
                    content: `Failed to remove restriction history with unique code \`${uniqueCode}\`.`,
                    ephemeral: true,
                });
            }
        } catch (err) {
            console.error('Error removing restriction history:', err);
            interaction.reply({
                content: 'Failed to remove restriction history. Please try again later.',
                ephemeral: true,
            });
        }
    }
};
