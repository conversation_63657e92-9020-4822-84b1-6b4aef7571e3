const { SlashCommandBuilder } = require('discord.js');
const dotenv = require('dotenv');
dotenv.config();

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('credits')
            .setDescription('View the credits for this bot'),
    },
};

module.exports.slashRun = async (interaction, client) => {
    try {
        // Defer the reply to keep the interaction alive 
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
        }

        const embed = client.embed()
            .setTitle('Credits')
            .setDescription('Acknowledgments for the creation and support of this bot:')
            .addFields(
                { name: 'Edited and Owned By', value: 'Timmycas | Nexoria Development', inline: false },
                { name: 'Ideas Helped From', value: 'Rich<PERSON>', inline: false },
                { name: 'Current Dev', value: 'Timmy', inline: false }
            )
            .setFooter({ text: `${process.env.copyright || '© Nexoria Development'}` })
            .setColor('#5865F2');

        // Edit the deferred reply with the embed
        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('Error executing credits command:', error);

        // Handle errors gracefully
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: 'An error occurred while processing your request. Please try again later.',
            });
        }
    }
};
