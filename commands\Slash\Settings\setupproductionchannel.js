const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: ['Administrator'],
        botPermissions: ['ManageChannels'],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('setupproductionchannel')
            .setDescription('Set up a voice channel to automatically show restricted user counts.')
            .addChannelOption(option =>
                option.setName('channel')
                    .setDescription('The voice channel to update with restricted user counts')
                    .setRequired(true)
                    .addChannelTypes(2) // Only allow GUILD_VOICE
            )
            .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if production stats are enabled
    if (process.env.SHOW_PRODUCTION_STATS !== 'true') {
        return interaction.reply({
            content: '❌ Production statistics are currently disabled. Please enable `SHOW_PRODUCTION_STATS=true` in your environment configuration.',
            ephemeral: true,
        });
    }

    const channel = interaction.options.getChannel('channel');
    
    if (channel.type !== 2) { // 2 = GUILD_VOICE
        return interaction.reply({ 
            content: 'Please select a voice channel.', 
            ephemeral: true 
        });
    }

    try {
        // Get current restricted user count from actual role members
        const Settings = require('../../../schemas/settings');
        const guildSettings = await Settings.findOne({ guildId: interaction.guild.id });
        const restrictedRoleId = guildSettings?.restrictionRoleId;

        let restrictedCount = 0;
        if (restrictedRoleId) {
            const restrictedRole = interaction.guild.roles.cache.get(restrictedRoleId);
            if (restrictedRole) {
                await interaction.guild.members.fetch(); // Ensure all members are cached
                restrictedCount = restrictedRole.members.size;
            }
        }

        // Update the channel name
        const newName = `Restricted: ${restrictedCount}`;
        await channel.setName(newName);

        const embed = interaction.client.embed()
            .setTitle('✅ Production Channel Setup Complete')
            .setDescription(`Voice channel has been configured to automatically display restricted user counts.`)
            .addFields(
                { name: '📺 Channel', value: `${channel.name}`, inline: true },
                { name: '🔒 Current Count', value: `${restrictedCount} restricted users`, inline: true },
                { name: '🔄 Update Frequency', value: 'Real-time + every 30 seconds', inline: true },
                { name: '⚙️ Auto-Update', value: 'Enabled when restrictions change', inline: false }
            )
            .setColor(process.env.DEFAULT_COLOUR || '#ADD8E6')
            .setFooter({ 
                text: `${process.env.copyright || '© Nexoria Development'} | Production Mode`,
                iconURL: interaction.guild.iconURL({ dynamic: true })
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        // Log the setup
        interaction.client.logs.info(`Production channel setup: ${channel.name} in ${interaction.guild.name}`);

    } catch (error) {
        console.error('Error setting up production channel:', error);
        
        await interaction.reply({
            content: '❌ An error occurred while setting up the production channel. Please check my permissions and try again.',
            ephemeral: true,
        });
    }
};
