const { ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const Settings = require('../../schemas/settings');

module.exports.conf = {
    Modal: {
        enabled: true,
    },
};

module.exports.help = {
    Modal: {
        id: 'appeal_ban_modal',
    },
};

module.exports.modalRun = async (interaction, client) => {
    if (interaction.customId === 'appeal_ban_modal') {
        const settings = await Settings.findOne({ guildId: interaction.guild.id });
        const appealLogChannelId = settings?.appealLogChannelId;

        if (!appealLogChannelId) {
            return interaction.reply({
                content: 'Appeals logging channel is not configured. Please set it up using the settings command.',
                ephemeral: true,
            });
        }

        const logChannel = await client.channels.fetch(appealLogChannelId);

        const embed = client.embed()
            .setTitle('Restriction Appeal Submission') // Updated title
            .setColor('#ffcc00')
            .setTimestamp()
            .setAuthor({ name: interaction.user.tag, iconURL: interaction.user.displayAvatarURL() })
            .addFields(
                { name: 'Why do you think you should be unbanned?', value: interaction.fields.getTextInputValue('reason_unban') || 'No response provided', inline: false },
                { name: 'What is your side of the story?', value: interaction.fields.getTextInputValue('side_of_story') || 'No response provided', inline: false },
                { name: 'What will you do to prevent this from happening again?', value: interaction.fields.getTextInputValue('prevent_future') || 'No response provided', inline: false }
            )
            .setFooter({ text: `${interaction.user.id}`, iconURL: interaction.user.displayAvatarURL() });

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setStyle(ButtonStyle.Secondary)
                    .setLabel('Accept')
                    .setCustomId('accept_appeal')
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setStyle(ButtonStyle.Primary)
                    .setLabel('Decline')
                    .setCustomId('decline_appeal')
                    .setEmoji('❌')
            );

        await logChannel.send({ embeds: [embed], components: [row1] });

        await interaction.reply({
            content: `Thank you ${interaction.user.tag}, your appeal has been sent to the higher-ups for further review`,
            ephemeral: true,
        });
    }
};

module.exports.buttonRun = async (interaction, client) => {
    if (interaction.customId === 'accept_appeal' || interaction.customId === 'decline_appeal') {
        const settings = await Settings.findOne({ guildId: interaction.guild.id });
        const staffRoleId = settings?.staffRoleId;

        if (!staffRoleId) {
            return interaction.reply({
                content: 'Staff role is not configured. Please set it up using the settings command.',
                ephemeral: true,
            });
        }

        const member = await interaction.guild.members.fetch(interaction.user.id);

        // Check if the user has the required staff role
        if (!member.roles.cache.has(staffRoleId)) {
            return interaction.reply({
                content: 'You do not have the required permissions to interact with these buttons.',
                ephemeral: true,
            });
        }

        const message = interaction.message;
        const embed = message.embeds[0];

        if (interaction.customId === 'accept_appeal') {
            embed.setColor('#00ff00').setTitle('Restriction Appeal Accepted'); // Update title and color
            await message.edit({ embeds: [embed], components: [] }); // Remove buttons
            await interaction.reply({ content: 'The appeal has been accepted and logged.', ephemeral: true });
        } else if (interaction.customId === 'decline_appeal') {
            embed.setColor('#ff0000').setTitle('Restriction Appeal Declined'); // Update title and color
            await message.edit({ embeds: [embed], components: [] }); // Remove buttons
            await interaction.reply({ content: 'The appeal has been declined and logged.', ephemeral: true });
        }
    }
};
