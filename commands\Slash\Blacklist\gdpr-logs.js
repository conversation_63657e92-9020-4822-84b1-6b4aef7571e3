const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { isDeveloper } = require('../../../middleware/localBlacklist');
const { sendGDPRLog } = require('../../../utils/gdprLogger');

module.exports.conf = {
    Slash: {
        enabled: true,
        userPermissions: [],
        botPermissions: [],
        isDefaultCooldown: 0,
        isPremiumCooldown: 0,
        isMaintenanceMode: false,
        isNSFW: false,
    },
};

module.exports.help = {
    Slash: {
        data: new SlashCommandBuilder()
            .setName('gdpr-logs')
            .setDescription('View GDPR erasure logs (Developer only)')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('View logs for specific user (optional)')
                    .setRequired(false))
            .addIntegerOption(option =>
                option.setName('limit')
                    .setDescription('Number of logs to show (default: 10, max: 50)')
                    .setRequired(false)
                    .setMinValue(1)
                    .setMaxValue(50)),
    },
};

module.exports.slashRun = async (interaction) => {
    // Check if user is a developer
    if (!isDeveloper(interaction.user.id)) {
        return interaction.reply({
            content: '❌ This command is restricted to developers only.',
            ephemeral: true
        });
    }

    const targetUser = interaction.options.getUser('user');
    const limit = interaction.options.getInteger('limit') || 10;

    try {
        const GDPRLog = require('../../../schemas/gdprLog');
        
        // Build query
        const query = {};
        if (targetUser) {
            query.user_id = targetUser.id;
        }

        // Fetch logs
        const logs = await GDPRLog.find(query)
            .sort({ timestamp: -1 })
            .limit(limit);

        if (logs.length === 0) {
            return interaction.reply({
                content: targetUser 
                    ? `📋 No GDPR logs found for ${targetUser.tag}`
                    : '📋 No GDPR logs found in the database.',
                ephemeral: true
            });
        }

        const embed = new EmbedBuilder()
            .setTitle('🔒 GDPR Erasure Logs')
            .setColor(0x800080)
            .setFooter({ 
                text: `Showing ${logs.length} log${logs.length !== 1 ? 's' : ''} | GDPR Compliance Audit`,
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();

        if (targetUser) {
            embed.setDescription(`**GDPR logs for:** ${targetUser.tag} (${targetUser.id})`);
        } else {
            embed.setDescription(`**Recent GDPR erasure requests**`);
        }

        // Add log entries
        let description = embed.data.description + '\n\n';
        
        for (let i = 0; i < Math.min(logs.length, 10); i++) {
            const log = logs[i];
            const logDate = new Date(log.timestamp);
            const timestamp = `<t:${Math.floor(logDate.getTime() / 1000)}:F>`;
            
            description += `**${i + 1}.** <@${log.user_id}>\n`;
            description += `├ **Reason:** ${log.reason}\n`;
            description += `├ **Flags:** ${log.flags.join(', ')}\n`;
            description += `├ **Issued by:** <@${log.Issuer}>\n`;
            description += `└ **Date:** ${timestamp}\n\n`;
        }

        if (logs.length > 10) {
            description += `*...and ${logs.length - 10} more logs*`;
        }

        embed.setDescription(description);

        // Add summary field
        const totalLogs = await GDPRLog.countDocuments(query);
        embed.addFields({
            name: '📊 Summary',
            value: `**Total GDPR Logs:** ${totalLogs}\n**Showing:** ${logs.length}/${totalLogs}`,
            inline: false
        });

        // Log GDPR log access for legal audit
        sendGDPRLog('GDPR_LOG_ACCESSED', {
            accessedBy: interaction.user.tag,
            accessedById: interaction.user.id,
            queryType: targetUser ? 'User-specific query' : 'General query',
            recordCount: logs.length,
            targetUser: targetUser?.tag,
            targetUserId: targetUser?.id,
            accessReason: 'GDPR compliance audit'
        });

        await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
        console.error('Error fetching GDPR logs:', error);
        await interaction.reply({
            content: '❌ An error occurred while fetching GDPR logs.',
            ephemeral: true
        });
    }
};
